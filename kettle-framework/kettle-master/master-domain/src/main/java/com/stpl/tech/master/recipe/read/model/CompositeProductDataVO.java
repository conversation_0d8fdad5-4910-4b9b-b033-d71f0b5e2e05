//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.01 at 01:01:52 PM IST 
//

package com.stpl.tech.master.recipe.read.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

import com.stpl.tech.master.recipe.model.CompositeProductData;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * Java class for CompositeProductData complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="CompositeProductData"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="maxQuantity" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="details" type="{http://www.w3schools.com}CompositeIngredientData" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CompositeProductDataVO", propOrder = { "maxQuantity", "details" })
public class CompositeProductDataVO implements Serializable {

	private static final long serialVersionUID = -3657030601925339886L;
	protected int maxQuantity;
	protected List<CompositeIngredientDataVO> details;
	protected Boolean isAllowMultiselection;

	public CompositeProductDataVO() {

	}

	/**
	 * @param compositeProduct
	 */
	public CompositeProductDataVO(CompositeProductData c) {
		if (c != null) {
			this.maxQuantity = c.getMaxQuantity();
			this.details = new ArrayList<>();
			c.getDetails().forEach(item -> {
				this.details.add(new CompositeIngredientDataVO(item));
			});
		}
	}

	/**
	 * Gets the value of the maxQuantity property.
	 * 
	 */
	public int getMaxQuantity() {
		return maxQuantity;
	}

	/**
	 * Sets the value of the maxQuantity property.
	 * 
	 */
	public void setMaxQuantity(int value) {
		this.maxQuantity = value;
	}

	/**
	 * Gets the value of the details property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the details property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getDetails().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link CompositeIngredientDataVO }
	 * 
	 * 
	 */
	public List<CompositeIngredientDataVO> getDetails() {
		if (details == null) {
			details = new ArrayList<CompositeIngredientDataVO>();
		}
		return this.details;
	}

	public Boolean getIsAllowMultiselection() { return this.isAllowMultiselection; }

	public void setIsAllowMultiselection(Boolean isAllowMultiselection) { this.isAllowMultiselection = isAllowMultiselection; }

}
