package com.stpl.tech.master.payment.model.razorpay;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RefundRequest {

    private String orderId;

    private String transactionId;

    private String refundId;

    private BigDecimal refundAmount;

    private String paymentPartnerType;

    private String refundReason;

    private String comments;

    // Additional fields that might be needed for different payment partners
    private String merchantId;
    private String merchantKey;
    private String terminalId;
    private String storeCode;
}
