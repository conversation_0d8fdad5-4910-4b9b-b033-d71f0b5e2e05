//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.01 at 01:01:52 PM IST 
//

package com.stpl.tech.master.recipe.read.model;

import com.stpl.tech.master.recipe.model.RecipeDetail;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * Java class for RecipeDetail complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="RecipeDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="recipeId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ingredient" type="{http://www.w3schools.com}IngredientDetail"/&gt;
 *         &lt;element name="addons" type="{http://www.w3schools.com}IngredientProductDetail" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="options" type="{http://www.w3schools.com}OptionDataVO" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RecipeDetailVO", propOrder = { "recipeId", "ingredient", "recommendations", "addons", "options" })
public class RecipeDetailVO implements Serializable {

	private static final long serialVersionUID = -8396561455897629757L;

	@XmlElement(required = true)
	protected int recipeId;
	@XmlElement(required = true)
	protected IngredientDetailVO ingredient;
	protected List<IngredientProductDetailVO> addons;
	protected List<IngredientProductDetailVO> recommendations;
	protected List<OptionDataVO> options;
	protected boolean isMilkBasedRecipe;

	protected int customizationCount;

	public RecipeDetailVO() {

	}

	/**
	 * @param recipe
	 */
	public RecipeDetailVO(RecipeDetail r) {
		this.recipeId = r.getRecipeId();
		this.customizationCount = r.getCustomizationCount();
		this.ingredient = new IngredientDetailVO(r.getIngredient());
		this.recommendations = new ArrayList<>();
		this.addons = new ArrayList<>();
		this.isMilkBasedRecipe = r.isMilkBasedRecipe();
		if (r.getAddons() != null) {
			r.getAddons().forEach(item -> {
				this.addons.add(new IngredientProductDetailVO(item));
			});
		}
		if (r.getRecommendations() != null) {
			r.getRecommendations().forEach(item -> {
				this.recommendations.add(new IngredientProductDetailVO(item));
			});
		}
		if (r.getTakeawayConsumables()!=null && !r.getTakeawayConsumables().isEmpty()) {
			r.getTakeawayConsumables().forEach(item -> {
				this.recommendations.add(new IngredientProductDetailVO(item));
			});
		}
		if (r.getDineInConsumables() != null && !r.getDineInConsumables().isEmpty()) {
			r.getDineInConsumables().forEach(item -> {
				this.recommendations.add(new IngredientProductDetailVO(item));
			});
		}
		if (r.getDeliveryConsumables() != null && !r.getDeliveryConsumables().isEmpty()) {
			r.getDeliveryConsumables().forEach(item -> {
				this.recommendations.add(new IngredientProductDetailVO(item));
			});
		}
		if (r.getOptions() != null) {
			this.options = new ArrayList<>();
			r.getOptions().forEach(item -> {
				this.options.add(new OptionDataVO(item));
			});
		}
	}

	/**
	 * Gets the value of the recipeId property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public int getRecipeId() {
		return recipeId;
	}

	/**
	 * Sets the value of the recipeId property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setRecipeId(int value) {
		this.recipeId = value;
	}

	/**
	 * Gets the value of the ingredient property.
	 * 
	 * @return possible object is {@link IngredientDetailVO }
	 * 
	 */
	public IngredientDetailVO getIngredient() {
		return ingredient;
	}

	/**
	 * Sets the value of the ingredient property.
	 * 
	 * @param value
	 *            allowed object is {@link IngredientDetailVO }
	 * 
	 */
	public void setIngredient(IngredientDetailVO value) {
		this.ingredient = value;
	}

	/**
	 * Gets the value of the addons property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the addons property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getAddons().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link IngredientProductDetailVO }
	 * 
	 * 
	 */
	public List<IngredientProductDetailVO> getAddons() {
		if (addons == null) {
			addons = new ArrayList<IngredientProductDetailVO>();
		}
		return this.addons;
	}
	
	public List<IngredientProductDetailVO> getRecommendations() {
		if (recommendations == null) {
			recommendations = new ArrayList<IngredientProductDetailVO>();
		}
		return this.recommendations;
	}

	public List<OptionDataVO> getOptions() {
		if (options == null) {
			options = new ArrayList<OptionDataVO>();
		}
		return this.options;
	}

	public int getCustomizationCount() {
		return customizationCount;
	}

	public void setCustomizationCount(int customizationCount) {
		this.customizationCount = customizationCount;
	}

	public boolean isMilkBasedRecipe() {
		return isMilkBasedRecipe;
	}

	public void setMilkBasedRecipe(boolean milkBasedRecipe) {
		isMilkBasedRecipe = milkBasedRecipe;
	}
}
