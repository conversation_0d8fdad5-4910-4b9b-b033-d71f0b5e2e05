package com.stpl.tech.master.recipe.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RecipeStep", propOrder = {"_id", "mediaType", "recipeStepName", "s3Key"})
@Document(collection = "recipeSteps")
public class RecipeStep implements Serializable {

    private static final long serialVersionUID = -6673185432699611870L;
    @Id
    private String _id;
    @Field
    private String mediaType;
    @Field
    private String recipeStepName;
    @Field
    private String s3Key;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public String getMediaType() {
        return mediaType;
    }

    public void setMediaType(String mediaType) {
        this.mediaType = mediaType;
    }

    public String getRecipeStepName() {
        return recipeStepName;
    }

    public void setRecipeStepName(String recipeStepName) {
        this.recipeStepName = recipeStepName;
    }

    public String getS3Key() {
        return s3Key;
    }

    public void setS3Key(String s3Key) {
        this.s3Key = s3Key;
    }

    @Override
    public String toString() {
        return "RecipeStep{" +
                "_id='" + _id + '\'' +
                ", mediaType='" + mediaType + '\'' +
                ", recipeStepName='" + recipeStepName + '\'' +
                ", s3Key='" + s3Key + '\'' +
                '}';
    }
}
