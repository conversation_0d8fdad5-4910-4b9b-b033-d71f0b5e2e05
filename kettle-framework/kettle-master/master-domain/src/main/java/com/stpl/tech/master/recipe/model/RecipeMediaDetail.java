package com.stpl.tech.master.recipe.model;


import com.stpl.tech.master.domain.model.Adapter2;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RecipeMediaDetail", propOrder = {"_id", "recipeId", "product", "dimension", "name", "profile", "recipeSteps"})
@Document(collection = "recipesMedia")
public class RecipeMediaDetail implements Serializable {

    private static final long serialVersionUID = -7810393834034551705L;
    @Id
    private String _id;
    @Field
    protected int recipeId;
    @Field
    protected ProductData product;
    @Field
    protected BasicInfo dimension;
    @Field
    protected String name;
    @Field
    protected String profile;
    @Field
    protected List<RecipeStep> recipeSteps;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public int getRecipeId() {
        return recipeId;
    }

    public void setRecipeId(int recipeId) {
        this.recipeId = recipeId;
    }

    public ProductData getProduct() {
        return product;
    }

    public void setProduct(ProductData product) {
        this.product = product;
    }

    public BasicInfo getDimension() {
        return dimension;
    }

    public void setDimension(BasicInfo dimension) {
        this.dimension = dimension;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public List<RecipeStep> getRecipeSteps() {
        return recipeSteps;
    }

    public void setRecipeSteps(List<RecipeStep> recipeSteps) {
        this.recipeSteps = recipeSteps;
    }

    @Override
    public String toString() {
        return "RecipeMediaDetail{" +
                "_id='" + _id + '\'' +
                ", recipeId=" + recipeId +
                ", product=" + product +
                ", dimension=" + dimension +
                ", name='" + name + '\'' +
                ", profile='" + profile + '\'' +
                ", recipeSteps=" + recipeSteps +
                '}';
    }
}
