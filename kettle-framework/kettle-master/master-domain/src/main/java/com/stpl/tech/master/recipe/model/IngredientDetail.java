//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.19 at 03:23:59 PM IST 
//

package com.stpl.tech.master.recipe.model;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IngredientDetail", propOrder = { "_id", "products", "variants", "components", "menuProducts" })
@Document(collection = "IngredientDetails")
public class IngredientDetail implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2576611384607231273L;

	@Id
	private String _id;

	@Field
	protected CompositeProductData compositeProduct;
	@Field
	protected List<IngredientProduct> products;
	@Field
	protected List<IngredientVariant> variants;
	@Field
	protected List<IngredientProductDetail> components;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public List<IngredientProduct> getProducts() {
		return this.products;
	}

	public List<IngredientVariant> getVariants() {
		return this.variants;
	}

	public List<IngredientProductDetail> getComponents() {
		return this.components;
	}

	public void setProducts(List<IngredientProduct> products) {
		this.products = products;
	}

	public void setVariants(List<IngredientVariant> variants) {
		this.variants = variants;
	}

	public void setComponents(List<IngredientProductDetail> components) {
		this.components = components;
	}

	public CompositeProductData getCompositeProduct() {
		return compositeProduct;
	}

	public void setCompositeProduct(CompositeProductData compositeProduct) {
		this.compositeProduct = compositeProduct;
	}

}
