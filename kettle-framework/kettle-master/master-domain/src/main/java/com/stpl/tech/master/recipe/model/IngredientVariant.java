//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.19 at 03:23:59 PM IST 
//

package com.stpl.tech.master.recipe.model;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IngredientVariant", propOrder = { "_id","product", "uom" , "status", "captured", "critical", "details"})
@Document(collection="IngredientVariants")
public class IngredientVariant implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3738896337470242421L;

	@Id
	private String _id;

	@Field
	protected ProductData product;
	@XmlElement(required = false)
	@XmlSchemaType(name = "string")
	@Field
	protected UnitOfMeasure uom;
	@Field
	protected String status = "ACTIVE";
	@Field
	protected boolean captured;
	@Field
	protected boolean customize;
	@Field
	protected boolean critical;
	@Field
	protected boolean dispensed;

	@Field
	protected String dispenseTag;

	@Field
	protected List<IngredientVariantDetail> details;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	/**
	 * Gets the value of the product property.
	 * 
	 * @return possible object is {@link ProductData }
	 * 
	 */
	public ProductData getProduct() {
		return product;
	}

	/**
	 * Sets the value of the product property.
	 * 
	 * @param value
	 *            allowed object is {@link ProductData }
	 * 
	 */
	public void setProduct(ProductData value) {
		this.product = value;
	}

	/**
	 * Gets the value of the uom property.
	 * 
	 * @return possible object is {@link UnitOfMeasure }
	 * 
	 */
	public UnitOfMeasure getUom() {
		return uom;
	}

	/**
	 * Sets the value of the uom property.
	 * 
	 * @param value
	 *            allowed object is {@link UnitOfMeasure }
	 * 
	 */
	public void setUom(UnitOfMeasure value) {
		this.uom = value;
	}

	/**
	 * Gets the value of the status property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStatus(String value) {
		this.status = value;
	}

	/**
	 * @return the captured
	 */
	public boolean isCaptured() {
		return captured;
	}

	/**
	 * @param captured
	 *            the captured to set
	 */
	public void setCaptured(boolean captured) {
		this.captured = captured;
	}

	/**
	 * Gets the value of the details property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the details property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getDetails().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link IngredientVariantDetail }
	 * 
	 * 
	 */
	public List<IngredientVariantDetail> getDetails() {
		return this.details;
	}

	public void setDetails(List<IngredientVariantDetail> details) {
		this.details = details;
	}

	public boolean isCritical() {
		return critical;
	}

	public void setCritical(boolean critical) {
		this.critical = critical;
	}

	public boolean isCustomize() {
		return customize;
	}

	public void setCustomize(boolean customize) {
		this.customize = customize;
	}

	public boolean isDispensed() {
		return dispensed;
	}

	public void setDispensed(boolean dispensed) {
		this.dispensed = dispensed;
	}

	public String getDispenseTag() {
		return dispenseTag;
	}

	public void setDispenseTag(String dispenseTag) {
		this.dispenseTag = dispenseTag;
	}
}
