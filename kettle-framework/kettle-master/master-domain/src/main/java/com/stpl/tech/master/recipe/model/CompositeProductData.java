package com.stpl.tech.master.recipe.model;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CompositeProductData", propOrder = { "_id", "maxQuantity", "details" })
@Document(collection = "CompositeProductDatas")
public class CompositeProductData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6220579205051084768L;

	@Id
	private String _id;
	@Field
	protected int maxQuantity;
	@Field
	protected List<CompositeIngredientData> details;

	@Field
	protected Boolean isAllowMultiselection;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public int getMaxQuantity() {
		return maxQuantity;
	}

	public void setMaxQuantity(int maxQuantity) {
		this.maxQuantity = maxQuantity;
	}

	public List<CompositeIngredientData> getDetails() {
		return details;
	}

	public void setDetails(List<CompositeIngredientData> details) {
		this.details = details;
	}

	public Boolean getIsAllowMultiselection() { return this.isAllowMultiselection; }

	public void setIsAllowMultiselection(Boolean isAllowMultiselection) { this.isAllowMultiselection = isAllowMultiselection; }

}
