package com.stpl.tech.master.recipe.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import lombok.Getter;
import lombok.Setter;

@ExcelSheet(value = "Recipe critical product update")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
@Getter
@Setter
public class RecipeProductExcelData {

    @ExcelField(headerName = "RECIPE_ID")
    private Integer recipeId;

    @ExcelField(headerName = "SCM_PRODUCT_IDS")
    private String productIds;
}
