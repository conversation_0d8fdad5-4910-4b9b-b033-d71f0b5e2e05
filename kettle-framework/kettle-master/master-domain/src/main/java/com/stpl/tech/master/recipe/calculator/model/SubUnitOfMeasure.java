//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.19 at 03:23:59 PM IST 
//

package com.stpl.tech.master.recipe.calculator.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

import com.stpl.tech.master.recipe.model.UnitOfMeasure;

@XmlType(name = "SubUnitOfMeasure")
@XmlEnum
public enum SubUnitOfMeasure {

	GM(UnitOfMeasure.KG, 1000), ML(UnitOfMeasure.L, 1000), PC(UnitOfMeasure.PC, 1), SACHET(UnitOfMeasure.SACHET, 1), PKT(UnitOfMeasure.PKT, 1);

	private final UnitOfMeasure uom;
	private final int ratio;

	private SubUnitOfMeasure(UnitOfMeasure uom, int ratio) {
		this.uom = uom;
		this.ratio = ratio;
	}

	public UnitOfMeasure getUom() {
		return uom;
	}

	public int getRatio() {
		return ratio;
	}

}
