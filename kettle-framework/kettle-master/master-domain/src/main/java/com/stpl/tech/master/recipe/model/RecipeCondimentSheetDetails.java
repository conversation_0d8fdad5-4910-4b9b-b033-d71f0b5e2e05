package com.stpl.tech.master.recipe.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

@ExcelSheet(value = "Recipe Condiment Sheet Detail Data")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class RecipeCondimentSheetDetails {

    @ExcelField(headerName = "PRODUCT_ID")
    public  Integer productId;

    @ExcelField(headerName = "PRODUCT_NAME")
    public String productName;

    @ExcelField(headerName = "DIMENSION")
    public String dimension;

    @ExcelField(headerName ="DINE_IN_CONDIMENT_GROUP_ID")
    public  Integer dineInCGId;

    @ExcelField(headerName ="DINE_IN_CONDIMENT_GROUP_NAME")
    public String dineInCGName;

    @ExcelField(headerName ="DINE_IN_CONDIMENT_GROUP_QUANTITY")
    public  Integer dineInCGQuantity;

    @ExcelField(headerName ="DELIVERY_CONDIMENT_GROUP_ID")
    public  Integer deliveryCGId;

    @ExcelField(headerName ="DELIVERY_CONDIMENT_GROUP_NAME")
    public String deliveryCGName;

    @ExcelField(headerName ="DELIVERY_CONDIMENT_GROUP_QUANTITY")
    public  Integer deliveryCGQuantity;

    @ExcelField(headerName ="TAKEAWAY_CONDIMENT_GROUP_ID")
    public  Integer takeawayCGId;

    @ExcelField(headerName ="TAKEAWAY_CONDIMENT_GROUP_NAME")
    public String takeawayCGName;

    @ExcelField(headerName ="TAKEAWAY_CONDIMENT_GROUP_QUANTITY")
    public  Integer takeawayCGQuantity;

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public Integer getDineInCGId() {
        return dineInCGId;
    }

    public void setDineInCGId(Integer dineInCGId) {
        this.dineInCGId = dineInCGId;
    }

    public String getDineInCGName() {
        return dineInCGName;
    }

    public void setDineInCGName(String dineInCGName) {
        this.dineInCGName = dineInCGName;
    }

    public Integer getDineInCGQuantity() {
        return dineInCGQuantity;
    }

    public void setDineInCGQuantity(Integer dineInCGQuantity) {
        this.dineInCGQuantity = dineInCGQuantity;
    }

    public Integer getDeliveryCGId() {
        return deliveryCGId;
    }

    public void setDeliveryCGId(Integer deliveryCGId) {
        this.deliveryCGId = deliveryCGId;
    }

    public String getDeliveryCGName() {
        return deliveryCGName;
    }

    public void setDeliveryCGName(String deliveryCGName) {
        this.deliveryCGName = deliveryCGName;
    }

    public Integer getDeliveryCGQuantity() {
        return deliveryCGQuantity;
    }

    public void setDeliveryCGQuantity(Integer deliveryCGQuantity) {
        this.deliveryCGQuantity = deliveryCGQuantity;
    }

    public Integer getTakeawayCGId() {
        return takeawayCGId;
    }

    public void setTakeawayCGId(Integer takeawayCGId) {
        this.takeawayCGId = takeawayCGId;
    }

    public String getTakeawayCGName() {
        return takeawayCGName;
    }

    public void setTakeawayCGName(String takeawayCGName) {
        this.takeawayCGName = takeawayCGName;
    }

    public Integer getTakeawayCGQuantity() {
        return takeawayCGQuantity;
    }

    public void setTakeawayCGQuantity(Integer takeawayCGQuantity) {
        this.takeawayCGQuantity = takeawayCGQuantity;
    }
}
