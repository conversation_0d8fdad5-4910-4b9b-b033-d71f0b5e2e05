package com.stpl.tech.master.recipe.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CondimentsDetail implements Serializable {


    private static final long serialVersionUID = -8327564414150988540L;
    private CondimentsData dineIn;
    private CondimentsData delivery;
    private CondimentsData takeaway;

}
