package com.stpl.tech.master.domain.model;

import lombok.Getter;

@Getter
public enum EmployeeEligibilityMappingColumn {
    // to string
    EMPLOYEE_ID("EMPLOYEE_ID"),
    EMPLOYEE_NAME("<PERSON><PERSON><PERSON><PERSON>YEE_NAME"),
    <PERSON><PERSON><PERSON><PERSON>YEE_CODE("<PERSON><PERSON><PERSON><PERSON>YEE_CODE"),
    MAPPING_TYPE("MAPPING_TYPE"),
    VALUE("VALUE"),
    UNIT_NAME("UNIT_NAME"),
    ELIGIBILITY_TYPE("ELIGIBILITY_TYPE"),
    STATUS("STATUS"),
    START_DATE("START_DATE"),
    END_DATE("END_DATE");

    private final String columnName;

    EmployeeEligibilityMappingColumn(String columnName) {
        this.columnName = columnName;
    }

}
