//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.19 at 03:23:59 PM IST 
//

package com.stpl.tech.master.recipe.calculator.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter2;
import com.stpl.tech.master.domain.model.Adapter4;
import com.stpl.tech.util.domain.adapter.BigDecimalSixPrecisionDeserializer;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RecipeIterationDetail", propOrder = { "_id", "iterationId", "iterationName", "linkedProductId",
		"linkedProductName", "productUom", "outputUom", "outputConversion", "productConversion", "outputQuantity",
		"status", "creationDate", "modificationDate", "components", "createdById", "createdByName", "lastUpdatedById",
		"lastUpdatedByName", "profile" })
@Document(collection = "recipeIterations")
public class RecipeIterationDetail implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1473689816337063678L;
	@Id
	private String _id;
	@Field
	protected int iterationId;
	@Field
	protected String iterationName;
	@Field
	protected Integer linkedProductId;
	@Field
	protected String linkedProductName;
	@Field
	protected String linkedConstructName;
	@Field
	protected SubUnitOfMeasure productUom;
	@Field
	protected SubUnitOfMeasure outputUom;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal outputConversion;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal productConversion;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal outputQuantity;
	@Field
	protected String status = RecipeIterationStatus.INITIATED;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date creationDate;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date modificationDate;
	@Field
	protected List<IterationIngredientDetail> components;
	@Field
	protected int createdById;
	@Field
	protected String createdByName;
	@Field
	protected int lastUpdatedById;
	@Field
	protected String lastUpdatedByName;

	@Field
	protected String notes;

	@Field
	protected List<String> imagesURL;

	@Field
	protected  String profile;
	/**
	 * attachment for steps to follow for the recipe
	 */
	@Field
	protected String productionPlan;

	@Field
	protected IterationReview review;

	public String get_id() {
		return _id;
	}

	public void set_id(String objectId) {
		this._id = objectId;
	}

	/**
	 * Gets the value of the recipeId property.
	 * 
	 */
	public int getIterationId() {
		return iterationId;
	}

	/**
	 * Sets the value of the recipeId property.
	 * 
	 */
	public void setIterationId(int value) {
		this.iterationId = value;

	}

	public String getIterationName() {
		return iterationName;
	}

	public Integer getLinkedProductId() {
		return linkedProductId;
	}

	public void setLinkedProductId(Integer linkedProductId) {
		this.linkedProductId = linkedProductId;
	}

	public String getLinkedProductName() {
		return linkedProductName;
	}

	public void setLinkedProductName(String linkedProductName) {
		this.linkedProductName = linkedProductName;
	}

	public void setIterationName(String iterationName) {
		this.iterationName = iterationName;
	}

	public SubUnitOfMeasure getProductUom() {
		return productUom;
	}

	public void setProductUom(SubUnitOfMeasure productUom) {
		this.productUom = productUom;
	}

	public SubUnitOfMeasure getOutputUom() {
		return outputUom;
	}

	public void setOutputUom(SubUnitOfMeasure outputUom) {
		this.outputUom = outputUom;
	}

	public BigDecimal getOutputConversion() {
		return outputConversion;
	}

	public void setOutputConversion(BigDecimal outputConversion) {
		this.outputConversion = outputConversion;
	}

	public BigDecimal getProductConversion() {
		return productConversion;
	}

	public void setProductConversion(BigDecimal productConversion) {
		this.productConversion = productConversion;
	}

	public BigDecimal getOutputQuantity() {
		return outputQuantity;
	}

	public void setOutputQuantity(BigDecimal outputQuantity) {
		this.outputQuantity = outputQuantity;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}

	public Date getModificationDate() {
		return modificationDate;
	}

	public void setModificationDate(Date modificationDate) {
		this.modificationDate = modificationDate;
	}

	public int getLastUpdatedById() {
		return lastUpdatedById;
	}

	public void setLastUpdatedById(int lastUpdatedById) {
		this.lastUpdatedById = lastUpdatedById;
	}

	public String getLastUpdatedByName() {
		return lastUpdatedByName;
	}

	public void setLastUpdatedByName(String lastUpdatedByName) {
		this.lastUpdatedByName = lastUpdatedByName;
	}

	public String getNotes() {
		return notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}

	public List<String> getImagesURL() {
		return imagesURL;
	}

	public void setImagesURL(List<String> imagesURL) {
		this.imagesURL = imagesURL;
	}

	public String getProductionPlan() {
		return productionPlan;
	}

	public void setProductionPlan(String productionPlan) {
		this.productionPlan = productionPlan;
	}

	public List<IterationIngredientDetail> getComponents() {
		if (components == null) {
			components = new ArrayList<>();
		}
		return components;
	}

	public int getCreatedById() {
		return createdById;
	}

	public void setCreatedById(int createdById) {
		this.createdById = createdById;
	}

	public String getCreatedByName() {
		return createdByName;
	}

	public void setCreatedByName(String createdByName) {
		this.createdByName = createdByName;
	}

	public String getLinkedConstructName() {
		return linkedConstructName;
	}

	public void setLinkedConstructName(String linkedConstructName) {
		this.linkedConstructName = linkedConstructName;
	}

	public IterationReview getReview() {
		return review;
	}

	public void setReview(IterationReview review) {
		this.review = review;
	}

	public String getProfile() {
		return profile;
	}

	public void setProfile(String profile) {
		this.profile = profile;
	}
}
