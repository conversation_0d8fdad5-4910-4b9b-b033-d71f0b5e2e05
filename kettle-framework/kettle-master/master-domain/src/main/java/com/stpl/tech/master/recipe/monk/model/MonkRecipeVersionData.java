/**
 *
 */
package com.stpl.tech.master.recipe.monk.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.stpl.tech.master.domain.model.Adapter2;
import com.stpl.tech.master.domain.model.UnitRegion;

/**
 * <AUTHOR>
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MonkRecipeVersionData", propOrder = { "_id", "version", "status", "genetrationTime", "updatedById",
		"updatedByName", "activationTime", "activatedById", "activatedByName", "closureTime", "closedById",
		"closedByName", "filePath", "changeLog", "content" })
@Document(collection = "monkRecipesVersions")
public class MonkRecipeVersionData implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 7925248473535304956L;
	@Id
	private String _id;
	@Field
	protected String version;
	@Field
	protected String status;
	@Field
	protected String region;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date genetrationTime;
	@Field
	protected int updatedById;
	@Field
	protected String updatedByName;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date activationTime;
	@Field
	protected Integer activatedById;
	@Field
	protected String activatedByName;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date closureTime;
	@Field
	protected Integer closedById;
	@Field
	protected String closedByName;
	@Field
	protected String filePath;
	@Field
	protected MonkRecipeSummaryData changeLog;
	@Field
	protected List<MonkRecipeData> content;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public Date getGenetrationTime() {
		return genetrationTime;
	}

	public void setGenetrationTime(Date genetrationTime) {
		this.genetrationTime = genetrationTime;
	}

	public int getUpdatedById() {
		return updatedById;
	}

	public void setUpdatedById(int updatedById) {
		this.updatedById = updatedById;
	}

	public String getUpdatedByName() {
		return updatedByName;
	}

	public void setUpdatedByName(String updatedByName) {
		this.updatedByName = updatedByName;
	}

	public Date getActivationTime() {
		return activationTime;
	}

	public void setActivationTime(Date activationTime) {
		this.activationTime = activationTime;
	}

	public Integer getActivatedById() {
		return activatedById;
	}

	public void setActivatedById(Integer activatedById) {
		this.activatedById = activatedById;
	}

	public String getActivatedByName() {
		return activatedByName;
	}

	public void setActivatedByName(String activatedByName) {
		this.activatedByName = activatedByName;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public MonkRecipeSummaryData getChangeLog() {
		return changeLog;
	}

	public void setChangeLog(MonkRecipeSummaryData changeLog) {
		this.changeLog = changeLog;
	}

	public List<MonkRecipeData> getContent() {
		return content;
	}

	public void setContent(List<MonkRecipeData> content) {
		this.content = content;
	}

	public Date getClosureTime() {
		return closureTime;
	}

	public void setClosureTime(Date closureTime) {
		this.closureTime = closureTime;
	}

	public Integer getClosedById() {
		return closedById;
	}

	public void setClosedById(Integer closedById) {
		this.closedById = closedById;
	}

	public String getClosedByName() {
		return closedByName;
	}

	public void setClosedByName(String closedByName) {
		this.closedByName = closedByName;
	}

}
