//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.19 at 03:23:59 PM IST 
//

package com.stpl.tech.master.recipe.model;


import com.stpl.tech.master.domain.model.TagValue;



import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import java.util.HashMap;

@XmlAccessorType(XmlAccessType.FIELD)


public class DispenserTagsMapping {


	private HashMap<String,TagValue> variants;
	private HashMap<String,TagValue> addOns;
	private HashMap<String,TagValue> mandatoryAddons;

	public HashMap<String, TagValue> getVariants() {
		return variants;
	}

	public void setVariants(HashMap<String, TagValue> variants) {
		this.variants = variants;
	}

	public HashMap<String, TagValue> getAddOns() {
		return addOns;
	}

	public void setAddOns(HashMap<String, TagValue> addOns) {
		this.addOns = addOns;
	}

	public HashMap<String, TagValue> getMandatoryAddons() {
		return mandatoryAddons;
	}

	public void setMandatoryAddons(HashMap<String, TagValue> mandatoryAddons) {
		this.mandatoryAddons = mandatoryAddons;
	}
}
