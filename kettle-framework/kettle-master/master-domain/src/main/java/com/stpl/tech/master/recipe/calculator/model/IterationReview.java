//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.19 at 03:23:59 PM IST 
//

package com.stpl.tech.master.recipe.calculator.model;

import java.io.Serializable;
import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.stpl.tech.master.domain.model.Adapter2;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IterationReview", propOrder = { "_id", "reviewId", "reviewContent", "reviewReason", "reviewedById",
		"reviewedByName", "reviewDate"})
@Document
public class IterationReview implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2724125928939936517L;

	@Id
	private String _id;
	
	@Field
	protected Integer reviewId;
	@Field
	protected String reviewContent;
	@Field
	protected String reviewReason;
	@Field
	protected Integer reviewedById;
	@Field
	protected String reviewedByName;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date reviewDate;
	
	
	
	public String get_id() {
		return _id;
	}

	public void set_id(String objectId) {
		this._id = objectId;
	}

	public Integer getReviewId() {
		return reviewId;
	}

	public void setReviewId(Integer reviewId) {
		this.reviewId = reviewId;
	}

	public String getReviewContent() {
		return reviewContent;
	}

	public void setReviewContent(String reviewContent) {
		this.reviewContent = reviewContent;
	}

	public String getReviewReason() {
		return reviewReason;
	}

	public void setReviewReason(String reviewReason) {
		this.reviewReason = reviewReason;
	}

	public Integer getReviewedById() {
		return reviewedById;
	}

	public void setReviewedById(Integer reviewedById) {
		this.reviewedById = reviewedById;
	}

	public String getReviewedByName() {
		return reviewedByName;
	}

	public void setReviewedByName(String reviewedByName) {
		this.reviewedByName = reviewedByName;
	}

	public Date getReviewDate() {
		return reviewDate;
	}

	public void setReviewDate(Date reviewDate) {
		this.reviewDate = reviewDate;
	}

}