package com.stpl.tech.master.domain.model.menu.excel.sheets;

import com.stpl.tech.master.domain.model.MenuType;
import com.stpl.tech.master.domain.model.menu.excel.MenuExcelUtil;
import com.stpl.tech.util.AppConstants;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

public enum MenuSequenceSlotColumns {

    MENU_NAME("MENU NAME", AppConstants.NOT_APPLICABLE,Boolean.TRUE,Boolean.FALSE,String.class,null),
    MENU_TYPE("MENU TYPE", AppConstants.NOT_APPLICABLE,Boolean.TRUE,Boolean.FALSE,String.class, MenuExcelUtil.getAcceptableMenuApps()),
    CATEGORY("Category", AppConstants.NOT_APPLICABLE,Boolean.TRUE,Boolean.TRUE,String.class,null),
    SUBCATEGORY("Subcategory", AppConstants.NOT_APPLICABLE,Boolean.TRUE,Boolean.FALSE,String.class,null),
    CATEGORY_GROUP_TAG("Category Group Tag", AppConstants.NOT_APPLICABLE,Boolean.TRUE,Boolean.TRUE,String.class,null),
    SUBCATEGORY_GROUP_TAG("SubCategory Group Tag", AppConstants.NOT_APPLICABLE,Boolean.TRUE,Boolean.FALSE,String.class,null),
    DEFAULT_CATEGORY_SEQ("Default (Cat Seq)", MenuType.DEFAULT.name(),true,Boolean.TRUE,BigDecimal.class,null) ,
    DEFAULT_SUBCATEGORY_SEQ("Default (Subcat Seq)",MenuType.DEFAULT.name(),true,Boolean.TRUE,BigDecimal.class,null),
    BREAKFAST_CATEGORY_SEQ("Breakfast (Cat Seq)", MenuType.DAY_SLOT_BREAKFAST.name(),true,Boolean.TRUE,BigDecimal.class,null) ,
    BREAKFAST_SUBCATEGORY_SEQ("Breakfast (Subcat Seq)",MenuType.DAY_SLOT_BREAKFAST.name(),true,Boolean.TRUE,
            BigDecimal.class,null),
    LUNCH_CATEGORY_SEQ("Lunch (Cat Seq)",MenuType.DAY_SLOT_LUNCH.name(),true,Boolean.TRUE, BigDecimal.class,null),
    LUNCH_SUBCATEGORY_SEQ("Lunch (Subcat Seq)",MenuType.DAY_SLOT_LUNCH.name(),true,Boolean.TRUE,BigDecimal.class,null),
    EVENING_CATEGORY_SEQ("Evening (Cat Seq)",MenuType.DAY_SLOT_EVENING.name(),true,Boolean.TRUE,BigDecimal.class,null),
    EVENING_SUBCATEGORY_SEQ("Evening (Subcat Seq)",MenuType.DAY_SLOT_EVENING.name(),true,Boolean.TRUE,BigDecimal.class,null),
    DINNER_CATEGORY_SEQ("Dinner (Cat Seq)",MenuType.DAY_SLOT_DINNER.name(),true,Boolean.TRUE,BigDecimal.class,null),
    DINNER_SUBCATEGORY_SEQ("Dinner (Subcat Seq)",MenuType.DAY_SLOT_DINNER.name(),true,Boolean.TRUE,BigDecimal.class,null),
    POST_DINNER_CATEGORY_SEQ("Post-Dinner (Cat Seq)",MenuType.DAY_SLOT_POST_DINNER.name(),true,Boolean.TRUE,
            BigDecimal.class,null),
    POST_DINNER_SUBCATEGORY_SEQ("Post-Dinner (Subcat Seq)",MenuType.DAY_SLOT_POST_DINNER.name(),true,Boolean.TRUE,
            BigDecimal.class,null),
    OVERNIGHT_CATEGORY_SEQ("Overnight (Cat Seq)",MenuType.DAY_SLOT_OVERNIGHT.name(),true,Boolean.TRUE,BigDecimal.class
            ,null),
    OVERNIGHT_SUBCATEGORY_SEQ("Overnight (Subcat Seq)",MenuType.DAY_SLOT_OVERNIGHT.name(),true,Boolean.TRUE,
            BigDecimal.class,null),

    UPDATE("UPDATE",AppConstants.NOT_APPLICABLE,Boolean.TRUE,Boolean.TRUE,String.class,MenuExcelUtil.getAcceptableUpdateValues()),
    IN_VALID_COLUMN("","",null,null,null,null);



    private String daySlot;

    private String columnName;

    private Boolean isMandatory;

    private Boolean nullable;

    private Class<?> dataType;

    private List<?> acceptableValues;


    MenuSequenceSlotColumns(String columnName  , String daySlot , Boolean isMandatory , Boolean nullable
    ,Class<?> dataType , List<?> acceptedValues){
        this.daySlot = daySlot;
        this.columnName = columnName;
        this.isMandatory  = isMandatory;
        this.nullable = nullable;
        this.dataType = dataType;
        this.acceptableValues = acceptedValues;
    }

    public String getColumnName(){
        return this.columnName;
    }

    public String getDaySlot(){
        return this.daySlot;
    }

    public Boolean isMandatory(){
        return this.isMandatory;
    }

    public Boolean nullable(){
        return this.nullable;
    }

    public Class<?> getDataType(){
        return this.dataType;
    }

    public List<?> getAcceptableValues(){return acceptableValues;}

    public static MenuSequenceSlotColumns getColumnEnum(String columnName){
        return Arrays.stream(MenuSequenceSlotColumns.values()).filter(column -> column.columnName.equalsIgnoreCase(columnName))
                .findFirst().orElse(MenuSequenceSlotColumns.IN_VALID_COLUMN);
    }


}
