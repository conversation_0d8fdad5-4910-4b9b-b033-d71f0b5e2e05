/**
 * 
 */
package com.stpl.tech.master.recipe.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter4;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.domain.adapter.BigDecimalSixPrecisionDeserializer;

/**
 * <AUTHOR>
 *
 */
public class RecipeCategoryCost implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1448913238902358041L;

	private Integer eventEntryId;

	private UnitCategory costType;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	private BigDecimal cost;
	private List<RecipeIngredientCost> ingredients;

	private List<String> errorCodes;

	public RecipeCategoryCost() {
		super();
	}
	
	

	public RecipeCategoryCost(UnitCategory costType) {
		super();
		this.costType = costType;
	}



	/**
	 * @return the costType
	 */
	public UnitCategory getCostType() {
		return costType;
	}

	/**
	 * @param costType
	 *            the costType to set
	 */
	public void setCostType(UnitCategory costType) {
		this.costType = costType;
	}

	/**
	 * @return the eventEntryId
	 */
	public Integer getEventEntryId() {
		return eventEntryId;
	}

	/**
	 * @param eventEntryId
	 *            the eventEntryId to set
	 */
	public void setEventEntryId(Integer eventEntryId) {
		this.eventEntryId = eventEntryId;
	}

	/**
	 * @return the cafeCost
	 */
	public BigDecimal getCost() {
		return cost;
	}

	/**
	 * @param cafeCost
	 *            the cafeCost to set
	 */
	public void setCost(BigDecimal cafeCost) {
		this.cost = cafeCost;
	}

	/**
	 * @return the ingredients
	 */
	public List<RecipeIngredientCost> getIngredients() {
		if (ingredients == null) {
			ingredients = new ArrayList<>();
		}
		return ingredients;
	}

	/**
	 * @param ingredients
	 *            the ingredients to set
	 */
	public void setIngredients(List<RecipeIngredientCost> ingredients) {
		this.ingredients = ingredients;
	}

	/**
	 * @return the errorCodes
	 */
	public List<String> getErrorCodes() {
		if (errorCodes == null) {
			errorCodes = new ArrayList<String>();
		}
		return errorCodes;
	}

	/**
	 * @param errorCodes
	 *            the errorCodes to set
	 */
	public void setErrorCodes(List<String> errorCodes) {
		this.errorCodes = errorCodes;
	}
}
