//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.05.19 at 03:23:59 PM IST
//


package com.stpl.tech.master.recipe.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for UnitOfMeasure.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="UnitOfMeasure"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="KG"/&gt;
 *     &lt;enumeration value="GM"/&gt;
 *     &lt;enumeration value="ML"/&gt;
 *     &lt;enumeration value="L"/&gt;
 *     &lt;enumeration value="PC"/&gt;
 *     &lt;enumeration value="PACKET"/&gt;
 *     &lt;enumeration value="SACHET"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 *
 */
@XmlType(name = "UnitOfMeasure")
@XmlEnum
public enum UnitOfMeasure {

    KG,
    L,
    PC,
    PKT,
    SACHET,
    SQ_FT,
    M,
    NOS,
    KM,
    FT,
    CUBIC_CM,
    CUBIC_M,
    SQ_MTR,
    ML;

    public String value() {
        return name();
    }

    public static UnitOfMeasure fromValue(String v) {
        return valueOf(v);
    }

}
