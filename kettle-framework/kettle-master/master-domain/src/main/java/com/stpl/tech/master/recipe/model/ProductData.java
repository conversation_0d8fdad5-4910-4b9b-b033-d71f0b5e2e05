/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.19 at 12:10:26 PM IST 
//

package com.stpl.tech.master.recipe.model;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Version;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.master.domain.model.ProductClassification;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductData", propOrder = { "_id","productId", "name" , "code", "shortCode", "status", "type", "subType", "isInventoryTracked"})
@Document(collection = "ProductDatas")
public class ProductData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -926969452764262475L;
	
	@Id
	private String _id;
	@Version
	@JsonIgnore
	private Long version;

	/**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 */
	private String detachAll;
	@Field
	protected int productId;
	@Field
	protected String name;
	@Field
	protected String displayName;
	@Field
	protected String code;
	@Field
	protected String shortCode;
	@Field
	protected String status;
	@Field
	protected int type;
	@Field
	protected int subType;
	@Field
	protected boolean variantLevelOrdering;
	@Field
	@XmlElement(required = false)
	@XmlSchemaType(name = "string")
	protected ProductClassification classification;
	@Field
	protected boolean isInventoryTracked;
	protected boolean isAutoProduction;
	protected Integer recipeId;

	
	public String get_id() {
		return _id;
	}

	public void set_id(String objectId) {
		this._id = objectId;
	}
	
	

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getDetachAll() {
		return detachAll;
	}

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}

	public int getProductId() {
		return productId;
	}

	public void setProductId(int id) {
		this.productId = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getShortCode() {
		return shortCode;
	}

	public void setShortCode(String shortCode) {
		this.shortCode = shortCode;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public boolean isInventoryTracked() {
		return isInventoryTracked;
	}

	public void setInventoryTracked(boolean isInventoryTracked) {
		this.isInventoryTracked = isInventoryTracked;
	}

	/**
	 * Gets the value of the type property.
	 * 
	 */
	public int getType() {
		return type;
	}

	/**
	 * Sets the value of the type property.
	 * 
	 */
	public void setType(int value) {
		this.type = value;
	}

	/**
	 * Gets the value of the subType property.
	 * 
	 */
	public int getSubType() {
		return subType;
	}

	/**
	 * Sets the value of the subType property.
	 * 
	 */
	public void setSubType(int value) {
		this.subType = value;
	}

	public boolean isVariantLevelOrdering() {
		return variantLevelOrdering;
	}

	public void setVariantLevelOrdering(boolean variantLevelOrdering) {
		this.variantLevelOrdering = variantLevelOrdering;
	}

	/**
	 * Gets the value of the isInventoryTracked property.
	 * 
	 */
	public boolean isIsInventoryTracked() {
		return isInventoryTracked;
	}

	/**
	 * Sets the value of the isInventoryTracked property.
	 * 
	 */
	public void setIsInventoryTracked(boolean value) {
		this.isInventoryTracked = value;
	}

	public ProductClassification getClassification() {
		return classification;
	}

	public void setClassification(ProductClassification classification) {
		this.classification = classification;
	}

	public boolean isAutoProduction() {
		return isAutoProduction;
	}

	public void setAutoProduction(boolean autoProduction) {
		isAutoProduction = autoProduction;
	}

	public Integer getRecipeId() {
		return recipeId;
	}

	public void setRecipeId(Integer recipeId) {
		this.recipeId = recipeId;
	}
}
