//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.19 at 03:23:59 PM IST 
//

package com.stpl.tech.master.recipe.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdName;

@XmlAccessorType(XmlAccessType.FIELD)

@Document(collection = "DispenserRecipeDetail")
public class DispenserRecipeDetail {

    @Id
    private String _id;
    @Field
    protected IdName product;
    @Field
    protected IdCodeName dimension;
    @Field
    protected String profile;
    @Field
    protected String recipeKey;
    @Field
    protected DispenserTagsMapping mapping;

    public IdName getProduct() {
        return product;
    }

    public void setProduct(IdName product) {
        this.product = product;
    }

    public IdCodeName getDimension() {
        return dimension;
    }

    public void setDimension(IdCodeName dimension) {
        this.dimension = dimension;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public String getRecipeKey() {
        return recipeKey;
    }

    public void setRecipeKey(String recipeKey) {
        this.recipeKey = recipeKey;
    }

    public DispenserTagsMapping getMapping() {
        return mapping;
    }

    public void setMapping(DispenserTagsMapping mapping) {
        this.mapping = mapping;
    }
}

