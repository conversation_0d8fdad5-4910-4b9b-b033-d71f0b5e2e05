package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.annotation.Nullable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Request DTO for Employee Eligibility Mapping
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeEligibilityMappingRequest {

    private Long id;

    @NotNull(message = "Eligibility type is required")
    private EligibilityType eligibilityType;

    @NotBlank(message = "Employee Code is required")
    private String employeeCode;

    @NotNull(message = "Mapping type is required")
    private EmployeeEligibilityMappingType mappingType;

    @NotNull(message = "Status is required")
    private SystemStatus status;

    @NotBlank(message = "Value is required")
    private String value;

    private Date startDate;

    private Date endDate;

    @Nullable
    private List<EmployeeEligibilityApprovalTypeRequest> approvalTypes = new ArrayList<>();
}
