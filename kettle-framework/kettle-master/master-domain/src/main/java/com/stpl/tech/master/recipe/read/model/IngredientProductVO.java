//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.01 at 01:01:52 PM IST 
//

package com.stpl.tech.master.recipe.read.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.springframework.beans.BeanUtils;

import com.stpl.tech.master.recipe.model.BasicInfo;
import com.stpl.tech.master.recipe.model.IngredientProduct;

/**
 * <p>
 * Java class for IngredientProduct complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="IngredientProduct"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="category" type="{http://www.w3schools.com}BasicInfo"/&gt;
 *         &lt;element name="details" type="{http://www.w3schools.com}IngredientProductDetail" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="critical" type="{http://www.w3schools.com}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IngredientProduct", propOrder = { "category", "details", "critical" })
public class IngredientProductVO implements Serializable {

	private static final long serialVersionUID = -8048758995037690493L;
	@XmlElement(required = true)
	protected BasicInfoVO category;
	protected String display;
	protected List<IngredientProductDetailVO> details;
	protected boolean critical;
	protected boolean customize;

	public IngredientProductVO() {

	}

	/**
	 * @param item
	 */
	public IngredientProductVO(IngredientProduct i) {
		if (i.getCategory() != null) {
			BasicInfoVO vo = new BasicInfoVO();
			BeanUtils.copyProperties(i.getCategory(), vo);
			this.category = vo;
		}
		this.critical = i.isCritical();
		this.customize = i.isCustomize();
		this.display = i.getDisplay();
		this.details = new ArrayList<>();
		i.getDetails().forEach(item -> {
			this.details.add(new IngredientProductDetailVO(item));
		});
	}

	/**
	 * Gets the value of the category property.
	 * 
	 * @return possible object is {@link BasicInfo }
	 * 
	 */
	public BasicInfoVO getCategory() {
		return category;
	}

	/**
	 * Sets the value of the category property.
	 * 
	 * @param value
	 *            allowed object is {@link BasicInfo }
	 * 
	 */
	public void setCategory(BasicInfoVO value) {
		this.category = value;
	}

	/**
	 * Gets the value of the details property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the details property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getDetails().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link IngredientProductDetailVO }
	 * 
	 * 
	 */
	public List<IngredientProductDetailVO> getDetails() {
		if (details == null) {
			details = new ArrayList<IngredientProductDetailVO>();
		}
		return this.details;
	}

	public boolean isCritical() {
		return critical;
	}

	public void setCritical(boolean critical) {
		this.critical = critical;
	}

	public String getDisplay() {
		return display;
	}

	public void setDisplay(String display) {
		this.display = display;
	}

	public boolean isCustomize() {
		return customize;
	}

	public void setCustomize(boolean customize) {
		this.customize = customize;
	}

}
