//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.19 at 03:23:59 PM IST 
//

package com.stpl.tech.master.recipe.model;

import java.io.Serializable;
import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.util.AppUtils;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RecipeUpdateLogDetail", propOrder = {
        "_id",
        "recipeId",
        "updatedBy",
        "updateTime",
        "updateLog"
})
@Document(collection = "recipeUpdateLog")
public class RecipeUpdateLogDetail implements Serializable {

    private static final long serialVersionUID = -8364248499941943050L;

    @Id
    private String _id;
    @Field
    protected int recipeId;
    @Field
    protected IdCodeName updatedBy;
    @Field
    protected Date updateTime = AppUtils.getCurrentTimestamp();
    @Field
    protected String updateLog;

    public String get_id() {
        return _id;
    }

    public void set_id(String objectId) {
        this._id = objectId;
    }

    public int getRecipeId() {
        return recipeId;
    }

    public void setRecipeId(int recipeId) {
        this.recipeId = recipeId;
    }

    public IdCodeName getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(IdCodeName updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateLog() {
        return updateLog;
    }

    public void setUpdateLog(String updateLog) {
        this.updateLog = updateLog;
    }
}
