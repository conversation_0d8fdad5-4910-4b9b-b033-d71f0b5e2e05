package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Request DTO for Employee Eligibility Approval Type
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeEligibilityApprovalTypeRequest {

    private Long id;

    @NotNull(message = "Mapping ID is required")
    private Long empEligibilityMappingId;

    @NotBlank(message = "Type is required")
    private String type;

    @NotNull(message = "Status is required")
    private SystemStatus status;
}
