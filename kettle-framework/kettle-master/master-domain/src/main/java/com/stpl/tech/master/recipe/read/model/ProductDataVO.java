//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.01 at 01:01:52 PM IST 
//


package com.stpl.tech.master.recipe.read.model;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

import com.stpl.tech.master.domain.model.ProductClassification;


/**
 * <p>Java class for ProductData complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ProductData"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="shortCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="type" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="subType" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="variantLevelOrdering" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="classification" type="{http://www.w3schools.com}ProductClassification"/&gt;
 *         &lt;element name="isInventoryTracked" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductDataVO", propOrder = {
    "productId",
    "name",
    "displayName",
    "shortCode",
    "type",
    "subType",
    "variantLevelOrdering",
    "classification",
    "isInventoryTracked"
})
public class ProductDataVO implements Serializable, Cloneable {

    private static final long serialVersionUID = 413717815758921455L;
    protected int productId;
    @XmlElement(required = true)
    protected String name;
    @XmlElement(required = true)
    protected String displayName;
    @XmlElement(required = true)
    protected String shortCode;
    protected int type;
    protected int subType;
    protected boolean variantLevelOrdering;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected ProductClassification classification;
    protected boolean isInventoryTracked;

    public Object clone() throws  CloneNotSupportedException{
        return super.clone();
    }

    /**
     * Gets the value of the productId property.
     * 
     */
    public int getProductId() {
        return productId;
    }

    /**
     * Sets the value of the productId property.
     * 
     */
    public void setProductId(int value) {
        this.productId = value;
    }

    /**
     * Gets the value of the name property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the displayName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * Sets the value of the displayName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setDisplayName(String value) {
        this.displayName = value;
    }

    /**
     * Gets the value of the shortCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShortCode() {
        return shortCode;
    }

    /**
     * Sets the value of the shortCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShortCode(String value) {
        this.shortCode = value;
    }

    /**
     * Gets the value of the type property.
     *
     */
    public int getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     * 
     */
    public void setType(int value) {
        this.type = value;
    }

    /**
     * Gets the value of the subType property.
     *
     */
    public int getSubType() {
        return subType;
    }

    /**
     * Sets the value of the subType property.
     *
     */
    public void setSubType(int value) {
        this.subType = value;
    }

    /**
     * Gets the value of the variantLevelOrdering property.
     * 
     */
    public boolean isVariantLevelOrdering() {
        return variantLevelOrdering;
    }

    /**
     * Sets the value of the variantLevelOrdering property.
     * 
     */
    public void setVariantLevelOrdering(boolean value) {
        this.variantLevelOrdering = value;
    }

    /**
     * Gets the value of the classification property.
     * 
     * @return
     *     possible object is
     *     {@link ProductClassification }
     *     
     */
    public ProductClassification getClassification() {
        return classification;
    }

    /**
     * Sets the value of the classification property.
     * 
     * @param value
     *     allowed object is
     *     {@link ProductClassification }
     *     
     */
    public void setClassification(ProductClassification value) {
        this.classification = value;
    }

    /**
     * Gets the value of the isInventoryTracked property.
     * 
     */
    public boolean isIsInventoryTracked() {
        return isInventoryTracked;
    }

    /**
     * Sets the value of the isInventoryTracked property.
     * 
     */
    public void setIsInventoryTracked(boolean value) {
        this.isInventoryTracked = value;
    }

}
