package com.stpl.tech.master.recipe.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter4;
import com.stpl.tech.util.domain.adapter.BigDecimalSixPrecisionDeserializer;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CompositeIngredientData", propOrder = { "_id", "name", "discount", "menuProducts", "status" })
@Document(collection = "CompositeIngredientDatas")
public class CompositeIngredientData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3473314520039812409L;
	@Id
	private String _id;
	@Field
	private String name;
	@Field
	private String status;
	@JsonDeserialize(using=BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	private BigDecimal discount;
	@Field
	private BigDecimal internalDiscount;
	@Field
	private String internalDiscountType;
	@Field
	protected List<IngredientProductDetail> menuProducts;
	@Field
	private Boolean customizable;
	@Field
	private Boolean discountApplicable;
	@Field
	private Integer maxSelection;
	@Field
	private Integer minSelection;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public List<IngredientProductDetail> getMenuProducts() {
		return menuProducts;
	}

	public void setMenuProducts(List<IngredientProductDetail> menuProducts) {
		this.menuProducts = menuProducts;
	}

	public BigDecimal getDiscount() {
		return discount;
	}

	public void setDiscount(BigDecimal discount) {
		this.discount = discount;
	}

	public BigDecimal getInternalDiscount() {
		return internalDiscount;
	}

	public void setInternalDiscount(BigDecimal internalDiscount) {
		this.internalDiscount = internalDiscount;
	}

	public String getInternalDiscountType() {
		return internalDiscountType;
	}

	public void setInternalDiscountType(String internalDiscountType) {
		this.internalDiscountType = internalDiscountType;
	}

	public Boolean isCustomizable() {
		return customizable;
	}

	public void setCustomizable(Boolean customizable) {
		this.customizable = customizable;
	}

	public Integer getMaxSelection() {
		return maxSelection;
	}

	public void setMaxSelection(Integer maxSelection) {
		this.maxSelection = maxSelection;
	}

	public Integer getMinSelection() {
		return minSelection;
	}

	public void setMinSelection(Integer minSelection) {
		this.minSelection = minSelection;
	}

	public Boolean isDiscountApplicable() {
		return discountApplicable;
	}

	public void setDiscountApplicable(Boolean discountApplicable) {
		this.discountApplicable = discountApplicable;
	}
}
