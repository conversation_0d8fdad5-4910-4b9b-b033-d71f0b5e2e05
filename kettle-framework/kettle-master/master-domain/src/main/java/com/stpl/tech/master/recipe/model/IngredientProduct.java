//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.19 at 03:23:59 PM IST 
//

package com.stpl.tech.master.recipe.model;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IngredientProduct", propOrder = { "_id","category", "display" , "status" , "captured", "critical", "details"})
@Document(collection="IngredientProducts")
public class IngredientProduct implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1498182977031669754L;

	@Id
	private String _id;

	@Field
	protected BasicInfo category;
	@Field
	protected String display;
	@Field
	protected String status = "ACTIVE";
	@Field
	protected boolean captured = true;
	@Field
	protected boolean critical;
	@Field
	protected boolean customize;
	@Field
	protected List<IngredientProductDetail> details;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}


	/**
	 * Gets the value of the category property.
	 * 
	 * @return possible object is {@link BasicInfo }
	 * 
	 */
	public BasicInfo getCategory() {
		return category;
	}

	/**
	 * Sets the value of the category property.
	 * 
	 * @param value
	 *            allowed object is {@link BasicInfo }
	 * 
	 */
	public void setCategory(BasicInfo value) {
		this.category = value;
	}

	/**
	 * Gets the value of the status property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStatus(String value) {
		this.status = value;
	}

	public boolean isCaptured() {
		return captured;
	}

	public void setCaptured(boolean captured) {
		this.captured = captured;
	}

	/**
	 * Gets the value of the details property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the details property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getDetails().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link IngredientProductDetail }
	 * 
	 * 
	 */
	public List<IngredientProductDetail> getDetails() {
		return this.details;
	}

	public void setDetails(List<IngredientProductDetail> details) {
		this.details = details;
	}

	public boolean isCritical() {
		return critical;
	}

	public void setCritical(boolean critical) {
		this.critical = critical;
	}

	public String getDisplay() {
		return display;
	}

	public void setDisplay(String display) {
		this.display = display;
	}

	public boolean isCustomize() {
		return customize;
	}

	public void setCustomize(boolean customize) {
		this.customize = customize;
	}

}
