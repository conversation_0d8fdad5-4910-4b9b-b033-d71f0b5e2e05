/**
 * 
 */
package com.stpl.tech.master.recipe.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class RecipeCost implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3909639660694434763L;

	private int recipeId;

	private String recipeName;

	private int productId;

	private String productName;

	private String dimension;

	List<RecipeIngredientCost> commonIngredient;

	List<RecipeCategoryCost> categoryCost;

	private List<String> errorCodes;

	public RecipeCost() {
		super();
	}

	public RecipeCost(int recipeId, String recipeName, int productId, String productName, String dimension) {
		super();
		this.recipeId = recipeId;
		this.recipeName = recipeName;
		this.productId = productId;
		this.productName = productName;
		this.dimension = dimension;
	}

	/**
	 * @return the recipeId
	 */
	public int getRecipeId() {
		return recipeId;
	}

	/**
	 * @param recipeId
	 *            the recipeId to set
	 */
	public void setRecipeId(int recipeId) {
		this.recipeId = recipeId;
	}

	/**
	 * @return the recipeName
	 */
	public String getRecipeName() {
		return recipeName;
	}

	/**
	 * @param recipeName
	 *            the recipeName to set
	 */
	public void setRecipeName(String recipeName) {
		this.recipeName = recipeName;
	}

	/**
	 * @return the productId
	 */
	public int getProductId() {
		return productId;
	}

	/**
	 * @param productId
	 *            the productId to set
	 */
	public void setProductId(int productId) {
		this.productId = productId;
	}

	/**
	 * @return the productName
	 */
	public String getProductName() {
		return productName;
	}

	/**
	 * @param productName
	 *            the productName to set
	 */
	public void setProductName(String productName) {
		this.productName = productName;
	}

	/**
	 * @return the dimension
	 */
	public String getDimension() {
		return dimension;
	}

	/**
	 * @param dimension
	 *            the dimension to set
	 */
	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	/**
	 * @return the categoryCost
	 */
	public List<RecipeCategoryCost> getCategoryCost() {
		if (categoryCost == null) {
			categoryCost = new ArrayList<>();
		}
		return categoryCost;
	}

	/**
	 * @param categoryCost
	 *            the categoryCost to set
	 */
	public void setCategoryCost(List<RecipeCategoryCost> categoryCost) {
		this.categoryCost = categoryCost;
	}

	/**
	 * @return the commonIngredient
	 */
	public List<RecipeIngredientCost> getCommonIngredient() {
		if (commonIngredient == null) {
			commonIngredient = new ArrayList<>();
		}
		return commonIngredient;
	}

	/**
	 * @param commonIngredient
	 *            the commonIngredient to set
	 */
	public void setCommonIngredient(List<RecipeIngredientCost> commonIngredient) {
		this.commonIngredient = commonIngredient;
	}

	/**
	 * @return the errorCodes
	 */
	public List<String> getErrorCodes() {
		if (errorCodes == null) {
			errorCodes = new ArrayList<String>();
		}
		return errorCodes;
	}

	/**
	 * @param errorCodes
	 *            the errorCodes to set
	 */
	public void setErrorCodes(List<String> errorCodes) {
		this.errorCodes = errorCodes;
	}

}
