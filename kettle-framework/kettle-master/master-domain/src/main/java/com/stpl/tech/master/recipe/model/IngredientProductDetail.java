//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.19 at 03:23:59 PM IST 
//

package com.stpl.tech.master.recipe.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter4;
import com.stpl.tech.master.recipe.calculator.model.IterationIngredientInstructions;
import com.stpl.tech.util.domain.adapter.BigDecimalSixPrecisionDeserializer;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IngredientProductDetail", propOrder = { "_id","product", "dimension" , "uom", "quantity", "defaultSetting", "critical", "status"})
@Document
public class IngredientProductDetail implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2724125928939936517L;

	@Id
	private String _id;
	/*@Version
	@JsonIgnore
	private Long version;

	*//**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 *//*
	private String detachAll;*/
	@Field
	protected ProductData product;
	@Field
	protected BasicInfo dimension;
	@XmlElement(required = false)
	@XmlSchemaType(name = "string")
	@Field
	protected UnitOfMeasure uom;
	@JsonDeserialize(using=BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal quantity;
	@JsonDeserialize(using=BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal oldQuantity;
	@Field
	protected boolean defaultSetting = false;
	@Field
	protected boolean customize;
	@Field
	protected boolean critical;
	@Field
	protected IngredientDetail ingredient;
	@Field
	protected List<IngredientProductDetail> addons;
	@Field
	protected String status = "ACTIVE";
	@JsonDeserialize(using=BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal yield = new BigDecimal(100d);
	protected List<IterationIngredientInstructions> instructions;
	@Field
	protected String tag;
	@Field
	protected String desc;
	@Field
	protected Boolean dispensed;
	@Field
	protected String dispenseTag;
	@Field
	protected Boolean showRecipe;
	@Field
	protected Instruction instruction;
	@Field
	protected Boolean display;
	@Field
	protected String displayCode;


	public String get_id() {
		return _id;
	}

	public void set_id(String objectId) {
		this._id = objectId;
	}

/*
	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getDetachAll() {
		return detachAll;
	}

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}*/

	/**
	 * Gets the value of the product property.
	 * 
	 * @return possible object is {@link ProductData }
	 * 
	 */
	public ProductData getProduct() {
		return product;
	}

	/**
	 * Sets the value of the product property.
	 * 
	 * @param value
	 *            allowed object is {@link ProductData }
	 * 
	 */
	public void setProduct(ProductData value) {
		this.product = value;
	}

	/**
	 * Gets the value of the dimension property.
	 * 
	 * @return possible object is {@link BasicInfo }
	 * 
	 */
	public BasicInfo getDimension() {
		return dimension;
	}

	/**
	 * Sets the value of the dimension property.
	 * 
	 * @param value
	 *            allowed object is {@link BasicInfo }
	 * 
	 */
	public void setDimension(BasicInfo value) {
		this.dimension = value;
	}

	/**
	 * Gets the value of the uom property.
	 * 
	 * @return possible object is {@link UnitOfMeasure }
	 * 
	 */
	public UnitOfMeasure getUom() {
		return uom;
	}

	/**
	 * Sets the value of the uom property.
	 * 
	 * @param value
	 *            allowed object is {@link UnitOfMeasure }
	 * 
	 */
	public void setUom(UnitOfMeasure value) {
		this.uom = value;
	}

	/**
	 * Gets the value of the quantity property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public BigDecimal getQuantity() {
		return quantity;
	}

	/**
	 * Sets the value of the quantity property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setQuantity(BigDecimal value) {
		this.quantity = value;
	}

	public BigDecimal getOldQuantity() {
		return oldQuantity;
	}

	public void setOldQuantity(BigDecimal oldQuantity) {
		this.oldQuantity = oldQuantity;
	}

	public boolean isDefaultSetting() {
		return defaultSetting;
	}

	public void setDefaultSetting(boolean defaultSetting) {
		this.defaultSetting = defaultSetting;
	}

	/**
	 * Gets the value of the status property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStatus(String value) {
		this.status = value;
	}

	public boolean isCritical() {
		return critical;
	}

	public void setCritical(boolean critical) {
		this.critical = critical;
	}

	public boolean isCustomize() {
		return customize;
	}

	public void setCustomize(boolean customize) {
		this.customize = customize;
	}

	public BigDecimal getYield() {
		return yield;
	}

	public void setYield(BigDecimal yield) {
		this.yield = yield;
	}

	public List<IterationIngredientInstructions> getInstructions() {
		return instructions;
	}

	public void setInstructions(List<IterationIngredientInstructions> instructions) {
		this.instructions = instructions;
	}

	public IngredientDetail getIngredient() {
		return ingredient;
	}

	public void setIngredient(IngredientDetail ingredient) {
		this.ingredient = ingredient;
	}

	public List<IngredientProductDetail> getAddons() {
		return addons;
	}

	public void setAddons(List<IngredientProductDetail> addons) {
		this.addons = addons;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public Boolean getDispensed() {
		return dispensed;
	}

	public void setDispensed(Boolean dispensed) {
		this.dispensed = dispensed;
	}

	public String getDispenseTag() {
		return dispenseTag;
	}

	public void setDispenseTag(String dispenseTag) {
		this.dispenseTag = dispenseTag;
	}

	public Boolean getShowRecipe() {
		return showRecipe;
	}

	public void setShowRecipe(Boolean showRecipe) {
		this.showRecipe = showRecipe;
	}

	public Instruction getInstruction() {
		return instruction;
	}

	public void setInstruction(Instruction instruction) {
		this.instruction = instruction;
	}

	public Boolean getDisplay() {
		return display;
	}

	public void setDisplay(Boolean display) {
		this.display = display;
	}

	public String getDisplayCode() {
		return displayCode;
	}

	public void setDisplayCode(String displayCode) {
		this.displayCode = displayCode;
	}
}
