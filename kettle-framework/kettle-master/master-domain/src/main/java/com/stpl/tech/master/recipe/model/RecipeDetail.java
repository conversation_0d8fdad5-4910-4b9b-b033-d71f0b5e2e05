//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.05.19 at 03:23:59 PM IST
//

package com.stpl.tech.master.recipe.model;

import com.stpl.tech.master.domain.model.Adapter2;
import com.stpl.tech.master.recipe.calculator.model.SubUnitOfMeasure;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.redis.core.RedisHash;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RecipeDetail", propOrder = { "_id", "recipeId", "product", "dimension", "name", "status",
		"creationDate", "modificationDate", "startDate", "endDate", "deliverable", "ingredient", "recommendations", "addons",
		"dineInConsumables", "takeawayConsumables","condiments" })
@Document(collection = "recipes")
@RedisHash("RecipeDetail")
public class RecipeDetail implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = -8770383427147819680L;
	@Id
	private String _id;
	@Field
	protected int recipeId;
	@Field
	protected ProductData product;
	@Field
	protected BasicInfo dimension;
	@Field
	protected String name;
	@Field
	protected String status = "ACTIVE";
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date creationDate;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date modificationDate;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date startDate;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date endDate;
	@Field
	protected boolean deliverable;
	@Field
	protected IngredientDetail ingredient;
	@Field
	protected int customizationCount;
	@Field
	protected List<IngredientProductDetail> addons;
	@Field
	protected List<IngredientProductDetail> mandatoryAddons;
	@Field
	protected List<OptionData> options;
	@Field
	protected List<IngredientProductDetail> recommendations;
	@Field
	protected List<IngredientProductDetail> dineInConsumables;
	@Field
	protected List<IngredientProductDetail> deliveryConsumables;
	@Field
	protected List<IngredientProductDetail> takeawayConsumables;
	@Field
	protected int lastUpdatedById;
	@Field
	protected String lastUpdatedByName;
	@Field
	protected Boolean containsCriticalProducts;

	@Field
	private CondimentsDetail condiments;

	@Field
	protected Integer approvalRequestedBy;
	@Field
	protected Integer approvedBy;

	protected String notes;

	protected List<String> imagesURL;

	protected String profile;

	protected  Boolean dispensed;

	protected String approvedByName;

	protected boolean isMilkBasedRecipe;

	protected SubUnitOfMeasure productUom;

	protected BigDecimal productConversion;

	protected SubUnitOfMeasure outputUom;

	protected BigDecimal outputConversion;

	public String get_id() {
		return _id;
	}

	public void set_id(String objectId) {
		this._id = objectId;
	}

	/**
	 * Gets the value of the recipeId property.
	 *
	 */
	public int getRecipeId() {
		return recipeId;
	}

	/**
	 * Sets the value of the recipeId property.
	 *
	 */
	public void setRecipeId(int value) {
		this.recipeId = value;
	}

	/**
	 * Gets the value of the product property.
	 *
	 * @return possible object is {@link ProductData }
	 *
	 */
	public ProductData getProduct() {
		return product;
	}

	/**
	 * Sets the value of the product property.
	 *
	 * @param value
	 *            allowed object is {@link ProductData }
	 *
	 */
	public void setProduct(ProductData value) {
		this.product = value;
	}

	/**
	 * Gets the value of the dimension property.
	 *
	 * @return possible object is {@link BasicInfo }
	 *
	 */
	public BasicInfo getDimension() {
		return dimension;
	}

	/**
	 * Sets the value of the dimension property.
	 *
	 * @param value
	 *            allowed object is {@link BasicInfo }
	 *
	 */
	public void setDimension(BasicInfo value) {
		this.dimension = value;
	}

	/**
	 * Gets the value of the name property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the status property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setStatus(String value) {
		this.status = value;
	}

	/**
	 * Gets the value of the creationDate property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getCreationDate() {
		return creationDate;
	}

	/**
	 * Sets the value of the creationDate property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setCreationDate(Date value) {
		this.creationDate = value;
	}

	/**
	 * Gets the value of the modificationDate property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getModificationDate() {
		return modificationDate;
	}

	/**
	 * Sets the value of the modificationDate property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setModificationDate(Date value) {
		this.modificationDate = value;
	}

	/**
	 * Gets the value of the startDate property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * Sets the value of the startDate property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setStartDate(Date value) {
		this.startDate = value;
	}

	/**
	 * Gets the value of the endDate property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * Sets the value of the endDate property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/**
	 * Gets the value of the deliverable property.
	 *
	 */
	public boolean isDeliverable() {
		return deliverable;
	}

	/**
	 * Sets the value of the deliverable property.
	 *
	 */
	public void setDeliverable(boolean value) {
		this.deliverable = value;
	}

	/**
	 * Gets the value of the ingredient property.
	 *
	 * @return possible object is {@link IngredientDetail }
	 *
	 */
	public IngredientDetail getIngredient() {
		return ingredient;
	}

	/**
	 * Sets the value of the ingredient property.
	 *
	 * @param value
	 *            allowed object is {@link IngredientDetail }
	 *
	 */
	public void setIngredient(IngredientDetail value) {
		this.ingredient = value;
	}

	/**
	 * Gets the value of the addons property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the addons property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getAddons().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link IngredientProductDetail }
	 *
	 *
	 */
	public List<IngredientProductDetail> getAddons() {
		return this.addons;
	}

	/**
	 * Gets the value of the dineInConsumbales property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the dineInConsumbales property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getDineInConsumbales().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link IngredientProductDetail }
	 *
	 *
	 */
	public List<IngredientProductDetail> getDineInConsumables() {
		return this.dineInConsumables;
	}

	/**
	 * Gets the value of the deliveryConsumables property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the deliveryConsumables property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getDeliveryConsumables().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link IngredientProductDetail }
	 *
	 *
	 */
	public List<IngredientProductDetail> getDeliveryConsumables() {
		return this.deliveryConsumables;
	}

	/**
	 * Gets the value of the takeawayConsumables property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the takeawayConsumables property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getTakeawayConsumables().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link IngredientProductDetail }
	 *
	 *
	 */
	public List<IngredientProductDetail> getTakeawayConsumables() {
		return this.takeawayConsumables;
	}

	public void setAddons(List<IngredientProductDetail> addons) {
		this.addons = addons;
	}

	public void setDineInConsumables(List<IngredientProductDetail> dineInConsumables) {
		this.dineInConsumables = dineInConsumables;
	}

	public void setDeliveryConsumables(List<IngredientProductDetail> deliveryConsumables) {
		this.deliveryConsumables = deliveryConsumables;
	}

	public void setTakeawayConsumables(List<IngredientProductDetail> takeawayConsumables) {
		this.takeawayConsumables = takeawayConsumables;
	}

	public List<String> getImagesURL() {
		return imagesURL;
	}

	public void setImagesURL(List<String> imagesURL) {
		this.imagesURL = imagesURL;
	}

	public int getCustomizationCount() {
		return customizationCount;
	}

	public void setCustomizationCount(int customizationCount) {
		this.customizationCount = customizationCount;
	}

	public int getLastUpdatedById() {
		return lastUpdatedById;
	}

	public void setLastUpdatedById(int lastUpdatedById) {
		this.lastUpdatedById = lastUpdatedById;
	}

	public String getLastUpdatedByName() {
		return lastUpdatedByName;
	}

	public void setLastUpdatedByName(String lastUpdatedByName) {
		this.lastUpdatedByName = lastUpdatedByName;
	}

	public Boolean getContainsCriticalProducts() {
		return containsCriticalProducts;
	}

	public void setContainsCriticalProducts(Boolean containsCriticalProducts) {
		this.containsCriticalProducts = containsCriticalProducts;
	}

	public List<OptionData> getOptions() {
		return options;
	}

	public void setOptions(List<OptionData> options) {
		this.options = options;
	}

	public String getNotes() {
		return notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}

	public String getProfile() {
		return profile;
	}

	public void setProfile(String profile) {
		this.profile = profile;
	}


	public List<IngredientProductDetail> getRecommendations() {
		return recommendations;
	}

	public void setRecommendations(List<IngredientProductDetail> recommendations) {
		this.recommendations = recommendations;
	}

	public Boolean getDispensed() {
		return dispensed;
	}

	public void setDispensed(Boolean dispensed) {
		this.dispensed = dispensed;
	}

	public List<IngredientProductDetail> getMandatoryAddons() {
		if (mandatoryAddons == null) {
			mandatoryAddons = new ArrayList<IngredientProductDetail>();
		}
		return mandatoryAddons;
	}

	public void setMandatoryAddons(List<IngredientProductDetail> mandatoryAddons) {
		this.mandatoryAddons = mandatoryAddons;
	}

	public CondimentsDetail getCondiments() {
		return condiments;
	}

	public void setCondiments(CondimentsDetail condiments) {
		this.condiments = condiments;
	}

	public Integer getApprovalRequestedBy() {
		return approvalRequestedBy;
	}

	public void setApprovalRequestedBy(Integer approvalRequestedBy) {
		this.approvalRequestedBy = approvalRequestedBy;
	}

	public Integer getApprovedBy() {
		return approvedBy;
	}

	public void setApprovedBy(Integer approvedBy) {
		this.approvedBy = approvedBy;
	}

	public String getApprovedByName() {
		return approvedByName;
	}

	public void setApprovedByName(String approvedByName) {
		this.approvedByName = approvedByName;
	}

	public boolean isMilkBasedRecipe() {
		return isMilkBasedRecipe;
	}

	public void setMilkBasedRecipe(boolean milkBasedRecipe) {
		isMilkBasedRecipe = milkBasedRecipe;
	}

	public SubUnitOfMeasure getProductUom() {
		return productUom;
	}

	public void setProductUom(SubUnitOfMeasure productUom) {
		this.productUom = productUom;
	}

	public BigDecimal getProductConversion() {
		return productConversion;
	}

	public void setProductConversion(BigDecimal productConversion) {
		this.productConversion = productConversion;
	}

	public SubUnitOfMeasure getOutputUom() {
		return outputUom;
	}

	public void setOutputUom(SubUnitOfMeasure outputUom) {
		this.outputUom = outputUom;
	}

	public BigDecimal getOutputConversion() {
		return outputConversion;
	}

	public void setOutputConversion(BigDecimal outputConversion) {
		this.outputConversion = outputConversion;
	}

}
