package com.stpl.tech.master.payment.model.patymNew;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class PaytmEDCTransactionResponseBody {
    private String paytmMid;
    private String paytmTid;
    private String transactionDateTime;
    private String merchantTransactionId;
    private String merchantReferenceNo;
    private String transactionAmount;
    private String acquirementId;
    private String retrievalReferenceNo;
    private Object authCode;
    private Object issuerMaskCardNo;
    private Object issuingBankName;
    private String bankResponseCode;
    private String bankResponseMessage;
    private Object bankMid;
    private Object bankTid;
    private String acquiringBank;
    private Object merchantExtendedInfo;
    private Object extendedInfo;
    private String aid;
    private String payMethod;
    private Object cardType;
    private Object cardScheme;
    private PaytmEDCTransactionResultInfo  resultInfo;
}