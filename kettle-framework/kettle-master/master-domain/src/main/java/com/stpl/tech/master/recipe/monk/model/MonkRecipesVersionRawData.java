package com.stpl.tech.master.recipe.monk.model;

import io.opencensus.trace.Link;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by Avesh on 16-12-2020.
 */
public class MonkRecipesVersionRawData {

    private String product;
    private String name;
    private String dimension;
    private String recipe;
    private String monkVersion;
    private String milkVariantMonkVersion;
    private LinkedHashMap<String,Integer> addons;

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getRecipe() {
        return recipe;
    }

    public void setRecipe(String recipe) {
        this.recipe = recipe;
    }

    public String getMonkVersion() {
        return monkVersion;
    }

    public void setMonkVersion(String monkVersion) {
        this.monkVersion = monkVersion;
    }

    public String getMilkVariantMonkVersion() {
        return milkVariantMonkVersion;
    }

    public void setMilkVariantMonkVersion(String milkVariantMonkVersion) {
        this.milkVariantMonkVersion = milkVariantMonkVersion;
    }

    public LinkedHashMap<String, Integer> getAddons() {
        return addons;
    }

    public void setAddons(LinkedHashMap<String, Integer> addons) {
        this.addons = addons;
    }

}
