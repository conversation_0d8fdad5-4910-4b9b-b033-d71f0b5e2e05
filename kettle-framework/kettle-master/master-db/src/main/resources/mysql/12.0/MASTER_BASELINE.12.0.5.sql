/*
 * Created for EMP_ELIGIBILITY_MAPPING_METADATA table and EMP_ELIGIBILITY_APPROVAL_TYPE table
 * Author: AI Assistant
 */

CREATE TABLE KETTLE_MASTER_DEV.EMP_ELIGIBILITY_MAPPING_METADATA (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    AP<PERSON>OVAL_TYPE VARCHAR(45) NOT NULL,
    STATUS VARCHAR(45) NOT NULL
);

-- Create the EMP_ELIGIBILITY_APPROVAL_TYPE table if it doesn't exist
CREATE TABLE IF NOT EXISTS KETTLE_MASTER_DEV.EMP_ELIGIBILITY_APPROVAL_TYPE (
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    EMP_ELIGIBILITY_MAPPING_ID BIGINT NOT NULL,
    TYPE VARCHAR(100) NOT NULL,
    STATUS ENUM('ACTIVE','IN_ACTIVE') DEFAULT 'ACTIVE',
    FOR<PERSON><PERSON><PERSON> KEY (EMP_ELIGIBILITY_MAPPING_ID) REFERENCES EMP_ELIGIBILITY_MAPPING(ID) ON DELETE CASCADE
);
