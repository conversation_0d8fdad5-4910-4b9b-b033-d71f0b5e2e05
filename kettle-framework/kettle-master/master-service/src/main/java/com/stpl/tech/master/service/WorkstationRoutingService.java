package com.stpl.tech.master.service;

import com.stpl.tech.master.dto.WorkstationRoutingRequest;
import com.stpl.tech.master.dto.WorkstationRoutingResponse;

import java.util.List;
import java.util.Map;


/**
 * Service for managing workstation routing configurations
 */
public interface WorkstationRoutingService {

    /**
     * Update workstation routing for a unit
     * @param request the workstation routing request
     * @return the workstation routing response
     */
    WorkstationRoutingResponse updateWorkstationRouting(WorkstationRoutingRequest request);

    /**
     * Get all product station mappings for a unit
     * @param unitId the unit ID
     * @return Map of refLookupId (station category) to list of product IDs
     */
    Map<Integer, List<Integer>> getAllProductStationMappingForUnit(Integer unitId);
} 