package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.service.MonkXTwoMetadataService;
import com.stpl.tech.master.core.service.model.MonkXTwoMetadataRequest;
import com.stpl.tech.master.data.model.MonkXTwoMetadata;
import com.stpl.tech.master.data.model.MonkXTwoMetadataResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.persistence.EntityNotFoundException;

import static com.stpl.tech.master.service.core.MasterServiceConstants.*;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + MONK_META_DATA)
public class MonkXTwoMetadataController {

    @Autowired
    private MonkXTwoMetadataService monkXTwoMetadataService;

    @PostMapping(value = "create-monk-metadata")
    public ResponseEntity<Boolean> createMonkMetadata(@RequestBody MonkXTwoMetadataRequest request) {
        try {
            MonkXTwoMetadata monkMetadata = monkXTwoMetadataService.createMonkMetadata(request);
            return ResponseEntity.status(HttpStatus.CREATED).body(Boolean.TRUE);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Boolean.FALSE);
        }

    }

    @GetMapping(value = "get-monk-metadata-by-unitId")
    public ResponseEntity<MonkXTwoMetadataResponse> getMonkMetaDataByUnitId(@RequestParam Integer unitId) {
        try {
            MonkXTwoMetadataResponse monkMetadata = monkXTwoMetadataService.getMonkMetaDataByUnitId(unitId);
            return ResponseEntity.status(HttpStatus.CREATED).body(monkMetadata);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }

    }

    @PostMapping(value = "update-monk-metadata-by-unitId")
    public ResponseEntity<Boolean> updateMonkMetadata(@RequestBody MonkXTwoMetadataRequest request) {
        try {
            return ResponseEntity.status(HttpStatus.CREATED).body(monkXTwoMetadataService.updateMonkMetadata(request));
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Boolean.FALSE);
        }

    }

}

