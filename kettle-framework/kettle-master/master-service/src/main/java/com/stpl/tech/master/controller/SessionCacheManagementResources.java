package com.stpl.tech.master.controller;

import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.service.SessionCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SESSION_CACHE_MANAGEMENT_ROOT_CONTEXT;

/**
 * Created by Rahul Singh on 09-07-2016.
 */
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + SESSION_CACHE_MANAGEMENT_ROOT_CONTEXT)
// 'v1/session-cache-management'
public class SessionCacheManagementResources {

    Logger LOG = LoggerFactory.getLogger(SessionCacheManagementResources.class);

    @Autowired
    MasterProperties props;

    @Autowired
    private SessionCacheService sessionCacheService;

    @Autowired
    private TaxDataCache taxDataCache;

    @Autowired
    @Qualifier(value = "MasterHazelCastInstance")
    private HazelcastInstance instance;

    @PostConstruct
    public void refreshSessionCacheOnReboot() {
        LOG.info("Refresh Session Cache On Reboot");
        try {
            if(instance.getCluster().getMembers().size()<=1 || taxDataCache.getTaxCategoryMap().size()<=0) {
                clearAllPosSession();
                LOG.info("Refresh Session Cache On Reboot :: SUCCESSFUL");
            }
            else{
                LOG.info("Session Cache Already Exist");
            }
        } catch (Exception e) {
            LOG.error("Refresh Session Cache On Reboot :: FAILED", e);
        }
    }


    @RequestMapping(method = RequestMethod.GET, value = "clear-all-pos-session")
    public boolean clearAllPosSession() {
        LOG.info("Request to remove all pos sessions");
        sessionCacheService.clearAllPosSession();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "clear-unit-pos-session")
    public boolean clearUnitPosSession(@RequestParam Integer unitId) {
        LOG.info("Request to remove all pos sessions for unit: " + unitId);
        sessionCacheService.clearUnitPosSession(unitId);
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "clear-unit-session")
    public boolean clearUnitSession(@RequestParam Integer unitId) {
        LOG.info("Request to remove all sessions for unit: " + unitId);
        sessionCacheService.clearUnitSession(unitId);
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "clear-user-pos-session")
    public boolean clearUserPosSession(@RequestParam Integer userId) {
        LOG.info("Request to remove all pos sessions for user: " + userId);
        sessionCacheService.clearUserPosSession(userId);
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "clear-user-session")
    public boolean clearUserSession(@RequestParam Integer userId) {
        LOG.info("Request to remove all sessions for user: " + userId);
        sessionCacheService.clearUserSession(userId);
        return true;
    }
}
