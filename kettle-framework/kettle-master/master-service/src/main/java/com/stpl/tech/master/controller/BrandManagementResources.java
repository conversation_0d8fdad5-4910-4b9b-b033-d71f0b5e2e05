/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.BrandManagementService;
import com.stpl.tech.master.data.model.BrandAttributes;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadata;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadataType;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.BrandMappingUpdateKey;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.BRAND_METADATA_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + BRAND_METADATA_ROOT_CONTEXT)
public class BrandManagementResources extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(BrandManagementResources.class);

    @Autowired
    private BrandManagementService brandManagementService;

    @Autowired
    private MasterDataCache masterDataCache;

    @RequestMapping(method = RequestMethod.GET, value = "brands", produces = MediaType.APPLICATION_JSON)
    public Collection<Brand> allBrands() {
        LOG.info("Getting All brands");
        return brandManagementService.getBrands();
    }

    @GetMapping("brands-short")
    public List<IdCodeName> getBrandsInShort(@RequestParam(required = false) Integer brandId) {
        return brandManagementService.getBrandsInShort(brandId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "all-brand-attributes", produces = MediaType.APPLICATION_JSON)
    public Collection<BrandAttributes> allBrandAttributes() {
        LOG.info("Getting All brand attributes");
        return brandManagementService.getAllBrandAttributes();
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-brand-attributes", produces = MediaType.APPLICATION_JSON)
    public boolean updateBrandAttributes(@RequestBody List<BrandAttributes> brandAttributes) {
        LOG.info("Updating brand attributes");
        return brandManagementService.updateBrandAttributes(brandAttributes);
    }


    /////////////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////// Unit Channel Partner Brand Mapping APIs //////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////


    @RequestMapping(method = RequestMethod.GET, value = "unit-partner-mapping", produces = MediaType.APPLICATION_JSON)
    public UnitPartnerBrandMappingData getAssociatedUnitPartnerMappingData(@RequestParam String restaurantId, @RequestParam int partnerId) {
        LOG.info("Get unit partner brand mapping for restaurant " + restaurantId + " and partner " + partnerId);
        return brandManagementService.getUnitPartnerMapping(restaurantId, partnerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "add/unit-partner-mapping", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public UnitPartnerBrandMappingData addUnitPartnerMappingData(@RequestBody UnitPartnerBrandMappingData unitPartnerBrandMappingData, @RequestParam(required = false) String editLiveDate) throws DataUpdationException {
        LOG.info("Adding unit partner mapping data");
        return brandManagementService.addUnitPartnerMapping(unitPartnerBrandMappingData,editLiveDate);
    }

    @RequestMapping(method = RequestMethod.POST, value = "add/all/unit-partner-mapping", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public List<UnitPartnerBrandMappingData> addAllUnitPartnerMappingData(@RequestBody List<UnitPartnerBrandMappingData> unitPartnerBrandMappingDatas) throws DataUpdationException {
        LOG.info("Adding unit partner mapping data");
        return brandManagementService.addAllUnitPartnerMapping(unitPartnerBrandMappingDatas);
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit-partner-brand-mappings", produces = MediaType.APPLICATION_JSON)
    public List<UnitPartnerBrandMappingData> getAllUnitPartnerBrandMappings() {
        LOG.info("Request to get all unit partner brand mappings");
        return brandManagementService.getAllUnitPartnerBrandMappings();
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-brand-mapping", produces = MediaType.APPLICATION_JSON)
    public UnitPartnerBrandMappingData getUnitPartnerBrandMapping(@RequestBody UnitPartnerBrandKey key) {
        LOG.info("Request to get unit partner brand mappings");
        return brandManagementService.getUnitPartnerBrandMapping(key);
    }

    @RequestMapping(method = RequestMethod.POST, value = "all-unit-partner-brand-mapping", produces = MediaType.APPLICATION_JSON)
    public List<UnitPartnerBrandMappingData> getAllUnitPartnerBrandMapping(@RequestBody UnitPartnerBrandKey key) {
        LOG.info("Request to get all unit partner brand mappings");
        return brandManagementService.getAllUnitPartnerBrandMapping(key.getBrandId(),key.getPartnerId());
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-brand-mapping/activate", produces = MediaType.APPLICATION_JSON)
    public UnitPartnerBrandMappingData activateUnitPartnerBrandMapping(@RequestBody Integer mappingId) throws DataUpdationException {
        return brandManagementService.activateUnitPartnerBrandMapping(mappingId, true);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-brand-mapping/deactivate", produces = MediaType.APPLICATION_JSON)
    public UnitPartnerBrandMappingData deactivateUnitPartnerBrandMapping(@RequestBody Integer mappingId) throws DataUpdationException {
        return brandManagementService.activateUnitPartnerBrandMapping(mappingId, false);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-brand-mapping/toggle-status",consumes = MediaType.APPLICATION_JSON)
    public boolean toggleUnitPartnerBrandMapping(@RequestBody BrandMappingUpdateKey key) throws DataUpdationException {
        LOG.info("Request to alter status of partners {}. New status will be {}",key.getMappingIds(), key.getStatus() );
        brandManagementService.toggleUnitPartnerBrandMapping(key.getMappingIds(),key.getStatus());
        return true;
    }


    /////////////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////// Unit Channel Partner Brand Mapping METADATA APIs /////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////


    @RequestMapping(method = RequestMethod.GET, value = "unit-partner-brand-metadata/keys",
        produces = MediaType.APPLICATION_JSON)
    public List<UnitPartnerBrandMappingMetadataType> getUnitPartnerBrandMappingMetadataKeys() {
        return Arrays.asList(UnitPartnerBrandMappingMetadataType.values());
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit-partner-brand-metadata",
        produces = MediaType.APPLICATION_JSON)
    public List<UnitPartnerBrandMappingMetadata> getUnitPartnerBrandMappingMetadataByKey(
        @RequestParam UnitPartnerBrandMappingMetadataType key) throws DataUpdationException {
        return brandManagementService.getUnitPartnerBrandMappingMetadataByKey(key);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-brand-metadata/add",
        produces = MediaType.APPLICATION_JSON)
    public UnitPartnerBrandMappingMetadata addUnitPartnerBrandMappingMetadata(
        @RequestBody UnitPartnerBrandMappingMetadata data) throws DataUpdationException {
        return brandManagementService.addUnitPartnerBrandMappingMetadata(data);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-brand-metadata/add/multiple",
        produces = MediaType.APPLICATION_JSON)
    public List<UnitPartnerBrandMappingMetadata> addUnitPartnerBrandMappingMetadataList(
        @RequestBody List<UnitPartnerBrandMappingMetadata> data) throws DataUpdationException {
        return brandManagementService.addUnitPartnerBrandMappingMetadataList(data);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-brand-metadata/activate",
        produces = MediaType.APPLICATION_JSON)
    public UnitPartnerBrandMappingMetadata activateUnitPartnerBrandMappingMetadata(@RequestBody Integer metadataId)
        throws DataUpdationException {
        return brandManagementService.changeStatusUnitPartnerBrandMappingMetadata(metadataId, true);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-brand-metadata/deactivate",
        produces = MediaType.APPLICATION_JSON)
    public UnitPartnerBrandMappingMetadata deactivateUnitPartnerBrandMappingMetadata(@RequestBody Integer metadataId)
        throws DataUpdationException {
        return brandManagementService.changeStatusUnitPartnerBrandMappingMetadata(metadataId, false);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-brand-metadata/get",
        produces = MediaType.APPLICATION_JSON)
    public Map<UnitPartnerBrandMappingMetadataType, String> getUnitPartnerBrandMappingMetadataByUnitPartnerBrand(
        @RequestBody UnitPartnerBrandKey key) {
        return brandManagementService.unitPartnerBrandMappingMetadataByUnitPartnerBrand(key);
    }

    @RequestMapping(method = RequestMethod.POST, value = "monk-meta-data", consumes = MediaType.MULTIPART_FORM_DATA)
    public Boolean updateMonkMetadata(HttpServletRequest request,
                                      @RequestParam(required = false, value = "mimeType") MimeType mimeType,
                                      @RequestParam(value = "brandId") Integer brandId,
                                      @RequestParam(value = "attributeKey") String attributeKey,
                                      @RequestParam(required = false,value = "attributeValue") String attributeValue,
                                      @RequestParam(value = "file")MultipartFile file ,
                                      @RequestParam(value = "isImage")Boolean isImage){
        return brandManagementService.uploadMonkMetaData(mimeType,brandId,attributeKey,attributeValue,file,isImage);
    }
}
