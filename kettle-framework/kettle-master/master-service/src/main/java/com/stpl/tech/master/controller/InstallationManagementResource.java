package com.stpl.tech.master.controller;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.InstallationService;
import com.stpl.tech.master.domain.model.ApplicationInstallationDetail;
import com.stpl.tech.master.domain.model.ApplicationInstallationRequest;
import com.stpl.tech.master.domain.model.UnitRestrictedApplicationDetail;
import com.stpl.tech.master.service.core.MasterServiceConstants;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + MasterServiceConstants.APPLICATION_INSTALLATION_ROOT_CONTEXT)
public class InstallationManagementResource extends AbstractResources {
	private static final Logger LOG = LoggerFactory.getLogger(InstallationManagementResource.class);

	@Autowired
	private InstallationService installationService;

	@RequestMapping(method = RequestMethod.GET, value = "installed-machine")
	@ResponseBody
	public List<ApplicationInstallationDetail> getInstalledMachines(@RequestParam String applicationName,
			@RequestParam Integer unitId, @RequestParam(required = false) String terminal,
			@RequestParam(required = false) String screenType, @RequestParam(required = false) String status) {
		LOG.info("getInstalledMachines application : " + applicationName + " for unit " + unitId);
		return installationService.getInstalledMachines(applicationName, unitId, terminal, screenType, status);
	}

	@RequestMapping(method = RequestMethod.POST, value = "add-machine")
	@ResponseBody
	public ApplicationInstallationDetail addInstallationMachine(
			@RequestBody ApplicationInstallationDetail installationDetail) {
		LOG.info("Installation request for application : " + installationDetail.getApplicationName() + " for unit "
				+ installationDetail.getUnit().getId());	
		return installationService.addInstallationMachine(installationDetail);
	}

	@RequestMapping(method = RequestMethod.POST, value = "update-machine-status")
	@ResponseBody
	public Boolean updateInstallationMachineStatus(@RequestBody ApplicationInstallationRequest request) {
		LOG.info("updateInstallationMachineStatus id "+ request.getId() );
		return installationService.updateInstallationMachineStatus(request);
	}
	
	@RequestMapping(method = RequestMethod.POST, value = "update-application-restriction")
	@ResponseBody
	public Boolean updateUnitRestrictedApplication(@RequestBody List<UnitRestrictedApplicationDetail> detail) {
		return installationService.updateUnitRestrictedApplication(detail);
	}
	
	@RequestMapping(method = RequestMethod.GET, value = "application-list")
	@ResponseBody
	public List<UnitRestrictedApplicationDetail> getApplicationList(@RequestParam Integer unitId, @RequestParam(required = false) String status) {
		return installationService.getApplicationList(unitId, status);
	}
	

}
