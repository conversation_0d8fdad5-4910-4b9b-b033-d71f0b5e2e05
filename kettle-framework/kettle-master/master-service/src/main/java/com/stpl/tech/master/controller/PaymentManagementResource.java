package com.stpl.tech.master.controller;


import com.stpl.tech.master.core.service.UnitManagementService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.model.PaymentMode;
import com.stpl.tech.master.data.model.UnitPaymentModeMapping;
import com.stpl.tech.master.domain.model.UnitPaymentModeMappingDetail;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;

import java.util.List;

import static com.stpl.tech.master.service.core.MasterServiceConstants.*;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + PAYMENT_MANAGEMENT_ROOT_CONTEXT)
public class PaymentManagementResource {

    private static final Logger LOG = LoggerFactory.getLogger(PaymentManagementResource.class);

    @Autowired
    private UnitManagementService unitManagementService;

    @RequestMapping(method = RequestMethod.POST, value = "add/payment-mapping", consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addPaymentMethod(@RequestBody PaymentMode paymentMode) {
        LOG.info("Saving data for payment mode : {}", JSONSerializer.toJSON(paymentMode));
        return unitManagementService.addPaymentMethod(paymentMode);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update/payment-mapping")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean updatePaymentMethod(@RequestBody com.stpl.tech.master.domain.model.PaymentMode paymentMode) {
        LOG.info("Update data for payment mode : {}", JSONSerializer.toJSON(paymentMode));
        return unitManagementService.updatePaymentMethod(MasterDataConverter.convert(paymentMode));
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-all/payment-mapping")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<com.stpl.tech.master.domain.model.PaymentMode> getPaymentMethod() {
        LOG.info("Fetching All Payment Mode");
        return unitManagementService.getPaymentMethod();
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-all/payment-partner-by-payment-type")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<String> getPaymentMethodByPaymentType(@RequestParam("paymentType")String paymentType) {
        LOG.info("Fetching All Payment Mode for type : {}",paymentType);
        return unitManagementService.getPaymentModeByPaymentType(paymentType);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-all/payment-mapping")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void updatePaymentModeMappingMethod(@RequestBody List<UnitPaymentModeMappingDetail> mapping) {
        LOG.info("Updating Payment Mode Mapping {}",mapping.size());
        unitManagementService.updatePaymentModeMappingMethod(mapping);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-all-unit/payment-mapping")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<UnitPaymentModeMappingDetail> getPaymentModeMapping(@RequestParam("id")Integer id){
        LOG.info("Fetching All Payment Mappings, for id :: {}",id);
        return unitManagementService.getPaymentModeMapping(id);
    }
//    @RequestMapping(method = RequestMethod.GET, value = "get-my-payment-mapping", produces = MediaType.APPLICATION_JSON)
//    public List<UnitPaymentModeMappingDetail> getMyPaymentMapping(){
//               return unitManagementService.getMyPaymentMapping();
//           }

}
