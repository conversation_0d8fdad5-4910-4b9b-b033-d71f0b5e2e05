/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.master.controller;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;
import static com.stpl.tech.master.service.core.MasterServiceConstants.TAX_METADATA_ROOT_CONTEXT;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.ws.rs.core.MediaType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.TaxDataCacheService;
import com.stpl.tech.master.core.service.TaxManagementService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.model.TaxCategoryData;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.tax.model.CategoryAdditionalTax;
import com.stpl.tech.master.tax.model.CategoryTax;
import com.stpl.tech.master.tax.model.TaxCategory;
import com.stpl.tech.master.tax.model.TaxInfo;
import com.stpl.tech.util.JSONSerializer;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR
		+ TAX_METADATA_ROOT_CONTEXT, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON)
public class TaxManagementResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(TaxManagementResource.class);

	@Autowired
	private TaxManagementService service;
	
	@Autowired
	private TaxDataCacheService cacheService;

	@RequestMapping(method = RequestMethod.GET, value = "countries")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<IdCodeName> getAllCountries() {
		LOG.info("Getting All Countries");
		return service.getAllCountries();
	}

	@RequestMapping(method = RequestMethod.GET, value = "categories")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<TaxCategory> getAllTaxCategories() {
		LOG.info("Getting All Tax Categories");
		return service.getAllTaxCategories();
	}

	@RequestMapping(value = "category")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public TaxCategory getTaxCategory(@RequestBody int id) {
		LOG.info("Getting Tax Category for Id {}", id);
		return service.getTaxCategory(id);
	}

	@RequestMapping(method = RequestMethod.GET, value = "categories/basic")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<IdCodeName> getAllTaxCategoriesBasicInfo() {
		LOG.info("Getting All Tax Categories Basic Info");
		return service.getAllTaxCategoriesBasicInfo();
	}

	@RequestMapping(value = "category/add")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public TaxCategory addTaxCategory(@RequestBody TaxCategory category) {
		LOG.info("Add Tax Category {}", JSONSerializer.toJSON(category));
		TaxCategory data = service.addTaxCategoy(category);
		cacheService.loadCache();
		return data;
	}
	
	@RequestMapping(value = "reload-cache")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean reloadCache() {
		LOG.info("Reload Tax Cache");
		cacheService.loadCache();
		return true;
	}

	@RequestMapping(value = "category/update")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public TaxCategory updateTaxCategoy(@RequestBody TaxCategory category) {
		LOG.info("update Tax Category {}", JSONSerializer.toJSON(category));
		return service.updateTaxCategoy(category);
	}

	@RequestMapping(value = "category/status")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean changeTaxCategoyStatus(@RequestBody TaxCategory category) throws DataUpdationException {
		LOG.info("update Tax Category status {}", JSONSerializer.toJSON(category));
		TaxCategoryData data = service.updateTaxCategoyStatus(category.getId(), category.getStatus());
		if (data != null) {
			TaxCategory taxCategory = MasterDataConverter.convert(data);
			taxCategory.setStatus(category.getStatus());
			cacheService.addTaxCategoryToCache(taxCategory);
			return true;
		}
		return false;
	}

	@RequestMapping(method = RequestMethod.GET, value = "tax/type")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<TaxInfo> getAllTaxes() {
		LOG.info("Getting all taxes");
		return service.getAllTaxes();
	}

	@RequestMapping(method = RequestMethod.GET, value = "tax/type/basic")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<IdCodeName> getAllTaxesBasicInfo() {
		LOG.info("Getting all taxes basic info");
		return service.getAllTaxesBasicInfo();
	}

	@RequestMapping(value = "tax/add")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean addTaxData(@RequestBody TaxInfo info) {
		LOG.info("Adding tax info {}", JSONSerializer.toJSON(info));
		return service.addTaxData(info);
	};

	@RequestMapping(method = RequestMethod.GET, value = "tax")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CategoryTax fetchCategoryTax(@RequestParam int countryId, @RequestParam int categoryId,
			@RequestParam int taxId) {
		LOG.info("Get Category Tax for Country Id: {},  category Id: {} , TaxId: {} ", countryId, categoryId, taxId);
		return service.fetchCategoryTax(countryId, categoryId, taxId, true);
	}

	@RequestMapping(value = "tax/update")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CategoryTax updateCategoryTax(@RequestBody CategoryTax categoryTax) {
		LOG.info("Updating tax info {}", JSONSerializer.toJSON(categoryTax));
		CategoryTax data = service.updateCategoryTax(categoryTax);
		List<CategoryTax> categories = new ArrayList<>();
		categories.add(data);
		CategoryAdditionalTax additionalTax = service.fetchCategoryAdditionalTax(categoryTax.getCountry().getId(),
				categoryTax.getCategory().getId());
		Map<String, CategoryAdditionalTax> additionalMap = new HashMap<>();
		if (additionalTax != null && additionalTax.getCategory() != null
				&& additionalTax.getCategory().getCode() != null) {
			additionalMap.put(additionalTax.getCategory().getCode(), additionalTax);
			cacheService.updateTaxCache(categories, additionalMap);
		}
		return data;
	}

	@RequestMapping(method = RequestMethod.GET, value = "tax/additional")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CategoryAdditionalTax fetchCategoryAdditionalTax(@RequestParam int countryId, @RequestParam int categoryId,
			@RequestParam int taxId) {
		LOG.info("Get Additional Category Tax for Country Id: {}, category Id: {} , TaxId: {} ", countryId, categoryId,
				taxId);
		return service.fetchCategoryAdditionalTax(countryId, categoryId, taxId);
	}

	@RequestMapping(value = "tax/additional/udpate")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CategoryAdditionalTax updateCategoryAdditionalTax(@RequestBody CategoryAdditionalTax categoryTax) {
		LOG.info("update Additional Category Tax  {} ", JSONSerializer.toJSON(categoryTax));
		CategoryAdditionalTax additionalTax = service.updateCategoryAdditionalTax(categoryTax);
		Map<String, CategoryAdditionalTax> additionalMap = new HashMap<>();
		additionalMap.put(additionalTax.getCategory().getCode(), additionalTax);
		List<CategoryTax> categories = new ArrayList<>();
		categories
				.add(service.fetchAllCategoryTax(categoryTax.getCountry().getId(), categoryTax.getCategory().getId()));
		cacheService.updateTaxCache(categories, additionalMap);
		return additionalTax;
	}

}
