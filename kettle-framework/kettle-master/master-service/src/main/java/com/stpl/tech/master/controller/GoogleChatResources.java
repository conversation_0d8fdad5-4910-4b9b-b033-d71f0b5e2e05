package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.external.cache.EnvironmentPropertiesCache;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.GoogleChatRequestBody;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.ws.rs.core.MediaType;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.GOOGLE_CHAT_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + GOOGLE_CHAT_ROOT_CONTEXT)
public class GoogleChatResources {

    private static final Logger LOG = LoggerFactory.getLogger(UserResources.class);

    @Autowired
    MasterProperties props;

    @RequestMapping(value = "send-notification-space", method = RequestMethod.POST,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void sendGoogleChatNotification(@RequestBody GoogleChatRequestBody googleChatRequestBody){
        LOG.info("Recieved request to send google chat notification to space : " + googleChatRequestBody.getSpace()
                + " with text : "+ googleChatRequestBody.getMessage());
        SlackNotificationService.getInstance().
                sendNotification(props.getEnvironmentType(),googleChatRequestBody.getUser(),null,googleChatRequestBody.getSpace(),googleChatRequestBody.getMessage());
    }
}
