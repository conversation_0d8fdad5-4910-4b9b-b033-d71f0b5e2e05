package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.core.service.RedisCacheService;
import com.stpl.tech.master.core.service.model.RecipeData;
import com.stpl.tech.master.domain.model.*;
import com.stpl.tech.master.readonly.domain.model.UnitProductData;
import com.stpl.tech.master.recipe.model.RecipeDetail;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.google.gson.Gson;

import static com.stpl.tech.master.service.core.MasterServiceConstants.*;

/**
 * Created by Chaayos on 01-10-2016.
 */
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + REDIS_CACHE_ROOT_CONTEXT) // 'v1/redis-cache'
public class RedisCacheResources {

    private static final Logger LOG = LoggerFactory.getLogger(RedisCacheResources.class);

    @Autowired
    private RedisCacheService redisCacheService;

    @RequestMapping(method = RequestMethod.GET, value = "unit/all", produces = MediaType.APPLICATION_JSON)
    public List<Unit> getAllUnits() throws DataNotFoundException {
        LOG.info("Request to get all units for redis cache");
        return redisCacheService.getAllUnits();
    }

    @RequestMapping(method = RequestMethod.GET, value = "location/all", produces = MediaType.APPLICATION_JSON)
    public List<Location> getAllLocations() throws DataNotFoundException {
        LOG.info("Request to get all states for redis cache");
        return redisCacheService.getAllLocations();
    }

    @RequestMapping(method = RequestMethod.GET, value = "ubd/all", produces = MediaType.APPLICATION_JSON)
    public List<UnitBasicDetail> getAllUnitBasicDetail() throws DataNotFoundException {
        LOG.info("Request to get all unit basic detail for redis cache");
        return redisCacheService.getAllUnitBasicDetail();
    }

    @RequestMapping(method = RequestMethod.GET, value = "all-kiosk-companies", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public List<KioskCompanyDetails> getAllKioskCompanies() throws DataNotFoundException {
        LOG.info("Request to get all units for redis cache");
        return redisCacheService.getAllKioskCompanies();
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit", produces = MediaType.APPLICATION_JSON)
    public Unit getUnit(@RequestBody int unitId) throws DataNotFoundException {
        LOG.info("Request to get unit for redis cache:" + unitId);
        return redisCacheService.getUnit(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-products", produces = MediaType.APPLICATION_JSON)
    public UnitProductData getUnitProducts(@RequestBody int unitId) throws DataNotFoundException {
        LOG.info("Request to get unit products for redis cache:" + unitId);
        return redisCacheService.getUnitProducts(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "transaction-metadata", produces = MediaType.APPLICATION_JSON)
    public TransactionMetadata getTransactionMetaData(@RequestBody int unitId) throws DataNotFoundException {
        LOG.info("Request to get unit for redis cache:" + unitId);
        return redisCacheService.getTransactionMetaData(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "ubd", produces = MediaType.APPLICATION_JSON)
    public UnitBasicDetail getUnitBasicDetail(@RequestBody int unitId) throws DataNotFoundException {
        LOG.info("Request to get unit basic detail for redis cache:" + unitId);
        return redisCacheService.getUnitBasicDetail(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "product/recipes/all", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public RecipeData getRecipes(@RequestBody RecipeData recipes) throws DataNotFoundException {
        LOG.info("Request to get recipe for redis cache for product ids {}", recipes.getProductIds());
        return redisCacheService.getRecipe(recipes);
    }

    @RequestMapping(method = RequestMethod.GET, value = "product/recipes", produces = MediaType.APPLICATION_JSON)
    public List<RecipeDetail> getRecipe(@RequestParam int productId) throws DataNotFoundException {
        LOG.info("Request to get recipe for redis cache for product id {}", productId);
        return redisCacheService.getRecipe(productId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "productBasicDetails", produces = MediaType.APPLICATION_JSON)
    public List<IdCodeName> getProducts() throws DataNotFoundException {
        LOG.info("Request to get all product basic details ");
        return redisCacheService.getProducts();
    }

    @RequestMapping(method = RequestMethod.GET, value = "categories/web", produces = MediaType.APPLICATION_JSON)
    public List<IdCodeName> getWebCategories() throws DataNotFoundException {
        LOG.info("Request to get web categories for redis cache");
        return redisCacheService.getWebCategories();
    }

    @RequestMapping(method = RequestMethod.GET, value = "recipe", produces = MediaType.APPLICATION_JSON)
    public RecipeDetail getRecipeDetails(@RequestParam int recipeId) throws DataNotFoundException {
        LOG.info("Request to get recipe for redis cache for recipe id {}", recipeId);
        return redisCacheService.getRecipeDetail(recipeId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "product/price/profiles/all", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, Map<Integer, List<ProductRecipeKey>>> getAllUnitProductPriceProfiles() {
        LOG.info("Request to get all unit product price profiles for redis cache");
        return redisCacheService.getAllUnitProductPriceProfiles();
    }

    @RequestMapping(method = RequestMethod.GET, value = "product/price/profiles/unit", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, List<ProductRecipeKey>> getProductPriceProfilesForUnit(@RequestParam Integer unitId) {
        LOG.info("Request to get product price profiles for unit id :{} for redis cache", unitId);
        return redisCacheService.getProductPriceProfilesForUnit(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "product/all-uploaded-product-image", produces = MediaType.APPLICATION_JSON)
    public ProductImageMappingDetailList getAllProductImages() {
        LOG.info("Request to get all product images");
        return redisCacheService.getAllProductImages();
    }

    @RequestMapping(method = RequestMethod.POST, value = "product/uploaded-product-images-for-productIds", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ProductImageMappingDetailList getProductImages(@RequestBody ProductImageMappingDetailList list) throws DataNotFoundException {
        LOG.info("Request to get all product images for productIds :{}");
        if(Objects.nonNull(list.getPids()) && !list.getPids().isEmpty()){
            return redisCacheService.getAllProductImages(list.getPids());
        }
        return new ProductImageMappingDetailList();
    }

}
