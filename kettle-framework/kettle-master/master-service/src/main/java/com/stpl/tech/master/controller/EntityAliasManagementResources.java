/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.BrandManagementService;
import com.stpl.tech.master.core.service.EntityAliasManagementService;
import com.stpl.tech.master.data.model.EntityAliasMappingData;
import com.stpl.tech.master.domain.model.EntityAliasKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.ws.rs.core.MediaType;
import java.util.Collection;
import java.util.List;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;
import static com.stpl.tech.master.service.core.MasterServiceConstants.ENTITY_ALIAS_ROOT_CONTEXT;

/**
 * Root resource (exposed at "pos-metadata" path)
 */

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + ENTITY_ALIAS_ROOT_CONTEXT)
public class EntityAliasManagementResources extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(EntityAliasManagementResources.class);

    @Autowired
    private EntityAliasManagementService entityAliasManagementService;


    @RequestMapping(method = RequestMethod.GET, value = "entity-alias", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Collection<EntityAliasMappingData> getAllEntityAlias() {
        LOG.info("Getting All entity Alias");
        return entityAliasManagementService.getAllEntityAlias();
    }

    @RequestMapping(value = "add/entity-alias", method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public EntityAliasMappingData addEntityAliasMapping(@RequestBody EntityAliasMappingData entityAliasMappingData) throws DataNotFoundException {
        return entityAliasManagementService.addEntityAlias(entityAliasMappingData);
    }

    @RequestMapping(value = "add/all/entity-alias", method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<EntityAliasMappingData>  addAllEntityAliasMapping(@RequestBody List<EntityAliasMappingData> entityAliasMappingDatas) throws DataNotFoundException {
        return entityAliasManagementService.addAllEntityAlias(entityAliasMappingDatas);
    }

    @RequestMapping(value = "update/entity-alias", method = RequestMethod.PUT,consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public EntityAliasMappingData updateEntityAliasMapping(@RequestBody EntityAliasMappingData entityAliasMappingData) throws DataNotFoundException {
        return entityAliasManagementService.updateEntityAlias(entityAliasMappingData);
    }



    @RequestMapping(method = RequestMethod.GET, value = "entity-alias-by-id-type", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Collection<EntityAliasMappingData> getEntityAliasbyEntityKeyAndEntityType(
            @RequestParam int entityId, @RequestParam String entityType) {
        LOG.info("Getting All entity Alias for entityId " + entityId + " entityType " + entityType);
        return entityAliasManagementService.getEntityAliasMappingByIdandType(entityId, entityType);
    }

    @RequestMapping(method = RequestMethod.GET, value = "entity-alias-by-id-type-brand", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public EntityAliasMappingData getEntityAliasbyEntityKeyAndEntityTypeandBrand(
            @RequestParam int entityId, @RequestParam String entityType, @RequestParam int brandId) {
        LOG.info("Getting All entity Alias for entityId " + entityId + " entityType " + entityType);
        return entityAliasManagementService.getEntityAliasMappingByIdandTypeandbrand(entityId, entityType, brandId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "entity-alias-list-by-key", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<EntityAliasMappingData> getEntityAliasbyEntityKey(@RequestBody List<EntityAliasKey> entityAliasKeys) {
        return entityAliasManagementService.getEntityMappingByEntityKey(entityAliasKeys);
    }


}
