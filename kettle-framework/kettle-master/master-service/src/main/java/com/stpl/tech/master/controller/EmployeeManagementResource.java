package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.service.EmployeeManagementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.util.Map;
import java.util.Set;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.EMPLOYEE_MANAGEMENT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + EMPLOYEE_MANAGEMENT)
public class EmployeeManagementResource {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeManagementResource.class);

    @Autowired
    private EmployeeManagementService employeeManagementService;

    @RequestMapping(method = RequestMethod.GET, value = "syncEmployeeData", produces = MediaType.APPLICATION_JSON)
    public Integer syncEmployeeData() {
        LOG.info("Calling employee sync API");
        return employeeManagementService.syncEmployeeData();
    }

    @RequestMapping(method = RequestMethod.GET, value = "lookUpDesignationDepartment", produces = MediaType.APPLICATION_JSON)
    public Map<String, Set<String>> lookUpDesignationDepartment() {
        return employeeManagementService.lookUpDesignationDepartment();
    }

    @RequestMapping(method = RequestMethod.GET, value = "getDesignationDepartmentMapping", produces = MediaType.APPLICATION_JSON)
    public Map<String, Set<String>> getDesignationDepartmentMapping() {
        return employeeManagementService.getDesignationDepartmentMapping();
    }
}
