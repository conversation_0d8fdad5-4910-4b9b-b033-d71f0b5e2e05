package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.LCDMenuImageService;
import com.stpl.tech.master.core.service.LCDMenuVariantManagementService;
import com.stpl.tech.master.data.model.LCDMenuImage;
import com.stpl.tech.master.data.model.LCDMenuImageFolder;
import com.stpl.tech.master.data.model.LCMMenuVariantMetadataGroup;
import com.stpl.tech.master.data.model.LCMMenuVariantMetadataItem;
import com.stpl.tech.master.domain.model.LCDMenuFolderRequest;
import com.stpl.tech.master.domain.model.LCDMenuImageDomain;
import com.stpl.tech.master.domain.model.LCDMenuImageMetaData;
import com.stpl.tech.master.domain.model.LCMMenuVariantMetadataGroupDomain;
import com.stpl.tech.master.domain.model.LCMMenuVariantMetadataItemDomain;
import com.stpl.tech.spring.service.FileArchiveService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.core.io.Resource;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.LCD_MENU_IMAGE_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;
import static com.stpl.tech.master.service.core.MasterServiceConstants.USER_SERVICES_ROOT_CONTEXT;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + LCD_MENU_IMAGE_ROOT_CONTEXT)
public class LCDMenuImageController extends AbstractResources {

    @Autowired
    private LCDMenuImageService imageService;

    @Autowired
    private FileArchiveService fileArchiveService;


    @Autowired
    private MasterProperties masterProperties;

    @Value("${lcdMenuImages.s3.bucket:dev.lcdmenu.images}")
    private String bucketName;


    // Get all versions
    @GetMapping("/versions")
    public ResponseEntity<List<String>> getAllVersions() {
        return ResponseEntity.ok(imageService.getAllVersions());
    }

    // Create new version
    @PostMapping("/versions")
    public ResponseEntity<Void> createVersion(HttpServletRequest request, @RequestParam(name = "VersionNo")
    String version) {
        if (StringUtils.isEmpty(version)) {
            return ResponseEntity.badRequest().build();
        }
        imageService.createVersion(version,getLoggedInUser(request));
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }

    // Get metadata (regions, price profiles, etc.)
    @GetMapping("/metadata")
    public ResponseEntity<LCDMenuImageMetaData> getMetadata() {
        return ResponseEntity.ok(imageService.getMetadata());
    }

    // Get images by version
    @GetMapping("/images/version/{version}")
    public ResponseEntity<List<LCDMenuImageDomain>> getImagesByVersion(@PathVariable String version) {
        return ResponseEntity.ok(imageService.getImagesByVersion(version));
    }

    @GetMapping("/images/search")
    public ResponseEntity<List<LCDMenuImageDomain>> searchImages(
            @RequestParam(value = "version", required = true) String version,
            @RequestParam(value = "region", required = false) String region,
            @RequestParam(value = "priceProfile", required = false) String priceProfile,
            @RequestParam(value = "orientation", required = false) String orientation,
            @RequestParam(value = "slot", required = false) String slot,
            @RequestParam(value = "lcdType", required = false) String lcdType) {

        List<LCDMenuImageDomain> images = imageService.searchImages(version, region, priceProfile, orientation, slot, lcdType);
        return ResponseEntity.ok(images);
    }

    /**
     * Search images using a map of parameters
     * @param params Map of parameter name to parameter value
     * @return List of matching images
     */
    @GetMapping("/images/search-by-params")
    public ResponseEntity<List<LCDMenuImageDomain>> searchImagesByParams(
            @RequestParam Map<String, String> params) {

        List<LCDMenuImageDomain> images = imageService.searchImagesByParams(params);
        return ResponseEntity.ok(images);
    }

    // Upload images (legacy method)
    @PostMapping("/images/upload")
    public ResponseEntity<Map<String, String>> uploadImages(
            HttpServletRequest request,
            @RequestParam("version") String version,
            @RequestParam("region") String region,
            @RequestParam("priceProfile") String priceProfile,
            @RequestParam("orientation") String orientation,
            @RequestParam("slot") String slot,
            @RequestParam("lcdType") String lcdType,
            @RequestParam(value = "variants", required = false) List<String> variants,
            @RequestParam("files") MultipartFile[] files) {

        try {
            List<String> uploadedFiles = imageService.uploadImages(
                    version, region, priceProfile, orientation, slot, lcdType,
                    variants, files,getLoggedInUser(request));

            Map<String, String> response = new HashMap<>();
            response.put("message", "Uploaded " + uploadedFiles.size() + " files successfully");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Upload images with a map of parameters
     * @param request HTTP request
     * @param params Map of parameter name to parameter value
     * @param variants List of variant values
     * @param files Array of files to upload
     * @return Response with upload status
     */
    @PostMapping("/images/upload-with-params")
    public ResponseEntity<Map<String, String>> uploadImagesWithParams(
            HttpServletRequest request,
            @RequestParam Map<String, String> params,
            @RequestParam(value = "variants", required = false) List<String> variants,
            @RequestParam("files") MultipartFile[] files) {

        try {
            List<String> uploadedFiles = imageService.uploadImagesWithParams(
                    params, variants, files, getLoggedInUser(request));

            Map<String, String> response = new HashMap<>();
            response.put("message", "Uploaded " + uploadedFiles.size() + " files successfully");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PostMapping("/images/update")
    public ResponseEntity<Map<String, String>> updateImage(
            HttpServletRequest request,
            @RequestParam("editImageId") Long editImageId,
            @RequestParam("file") MultipartFile file) {
        try {
            String uploadedFile = imageService.updateImage(
                    editImageId, file,getLoggedInUser(request));

            Map<String, String> response = new HashMap<>();
            response.put("message", "Updated " + uploadedFile + " file successfully");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/images/download")
    public ResponseEntity<byte[]> downloadImages(
            @RequestParam String version,
            @RequestParam(required = false) String region,
            @RequestParam(required = false) String priceProfile,
            @RequestParam(required = false) String orientation,
            @RequestParam(required = false) String slot,
            @RequestParam(required = false) String lcdType) throws IOException {

        // Convert parameters to a map
        Map<String, String> params = new HashMap<>();
        params.put("version", version);
        if (region != null) params.put("region", region);
        if (priceProfile != null) params.put("priceProfile", priceProfile);
        if (orientation != null) params.put("orientation", orientation);
        if (slot != null) params.put("slot", slot);
        if (lcdType != null) params.put("lcdType", lcdType);

        return downloadImagesByParams(params);
    }

    /**
     * Download images using a map of parameters
     * @param params Map of parameter name to parameter value
     * @return Zip file containing matching images
     * @throws IOException If there's an error creating the zip file
     */
    @GetMapping("/images/download-by-params")
    public ResponseEntity<byte[]> downloadImagesByParams(
            @RequestParam Map<String, String> params) throws IOException {

        // Search for images with the given parameters
        List<LCDMenuImageDomain> images = imageService.searchImagesByParams(params);

        // Create zip file in memory
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ZipOutputStream zos = new ZipOutputStream(baos);

        for (LCDMenuImageDomain image : images) {
            // Download image from S3
            byte[] imageData = fileArchiveService.downloadFile(image.getPath(),bucketName+"/lcd-media");

 /*           // Create folder structure in zip
            String folderPath = String.format("%s/%s/%s/%s/%s/%s/",
                    image.getVersion(), image.getRegion(), image.getPriceProfile(),
                    image.getOrientation(), image.getSlot(), image.getLcdType());*/

//            String folderPath = buildFolderPath(
//                    image.getVersion(),
//                    image.getRegion(),
//                    image.getPriceProfile(),
//                    image.getOrientation(),
//                    image.getSlot(),
//                    image.getLcdType()
//            );

            String folderPath = imageService.buildFolderPathFiltered(params);

            // Add to zip
            ZipEntry entry = new ZipEntry(folderPath + image.getName());
            zos.putNextEntry(entry);
            zos.write(imageData);
            zos.closeEntry();
        }

        zos.close();

        // Return zip file
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "lcd-menu-images.zip");

        return new ResponseEntity<>(baos.toByteArray(), headers, HttpStatus.OK);
    }

    // Helper method to build a path, stopping at the first null value
    private String buildFolderPath(String... values) {
        StringBuilder path = new StringBuilder();

        for (String value : values) {
            if (value == null || value.isEmpty()) {
                break; // Stop appending when a null value is encountered
            }
            path.append(value).append("/");
        }

        // Remove trailing slash if present
        if (path.length() > 0 && path.charAt(path.length() - 1) == '/') {
            path.deleteCharAt(path.length() - 1);
        }

        return path.toString();
    }

    @GetMapping("/folders/{step}")
    public ResponseEntity<List<LCDMenuImageFolder>> getFolders(@PathVariable String step) {
        List<LCDMenuImageFolder> folders = imageService.getFoldersByStep(step);
        return ResponseEntity.ok(folders);
    }

    @GetMapping("/files/{step}")
    public ResponseEntity<List<LCDMenuImageDomain>> getFilesForStep(
            @PathVariable String step,
            @RequestParam Map<String, String> params) {
        List<LCDMenuImageDomain> files = imageService.getFilesForStep(step, params);
        return ResponseEntity.ok(files);
    }

    @GetMapping("/folders/{step}/withParams")
    public ResponseEntity<List<LCDMenuImageFolder>> getFoldersForStep(
            @PathVariable String step,
            @RequestParam Map<String, String> params) {
        List<LCDMenuImageFolder> folders = imageService.getFoldersForStep(step, params);
        return ResponseEntity.ok(folders);
    }

    @PostMapping("/folders")
    public ResponseEntity<LCDMenuImageFolder> createFolder(@RequestBody LCDMenuFolderRequest request) {
        try {
            LCDMenuImageFolder folder = imageService.createFolder(request.getStep(), request.getName());
            return ResponseEntity.ok(folder);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @DeleteMapping("/folders/{step}/{name}")
    public ResponseEntity<Void> deleteFolder(@PathVariable String step, @PathVariable String name) {
        try {
            imageService.deleteFolder(step, name);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @DeleteMapping("/images/{imageId}")
    public ResponseEntity<Void> deleteImage(@PathVariable Long imageId) {
        try {
            imageService.deactivateImage(imageId);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

   /* // Delete image
    @DeleteMapping("/images/{id}")
    public ResponseEntity<Void> deleteImage(@PathVariable String id) {
        boolean deleted = imageService.deleteImage(id);
        if (deleted) {
            return ResponseEntity.noContent().build();
        } else {
            return ResponseEntity.notFound().build();
        }
    }*/

    @GetMapping("/steps")
    public ResponseEntity<List<String>> getAllSteps() {
        return ResponseEntity.ok(imageService.getAllSteps());
    }

    @PostMapping("/steps")
    public ResponseEntity<Void> createStep(@RequestParam String name) {
        imageService.createNewStep(name);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/download/zip")
    public ResponseEntity<Resource> downloadZip(
            @RequestParam String path,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        // Existing zip download logic with date range
        return null; // Placeholder return, actual implementation needed
    }

    @GetMapping("/download/excel")
    public ResponseEntity<Resource> downloadExcel(
            @RequestParam String path,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        // Existing excel download logic with date range
        return null; // Placeholder return, actual implementation needed
    }

    // variant management

    @Autowired
    private LCDMenuVariantManagementService variantManagementService;

    // Group endpoints
    @GetMapping("/groups")
    public ResponseEntity<List<LCMMenuVariantMetadataGroupDomain>> getAllGroups() {
        return ResponseEntity.ok(variantManagementService.getAllGroups());
    }

    @PostMapping("/groups")
    public ResponseEntity<LCMMenuVariantMetadataGroupDomain> createGroup(@RequestBody LCMMenuVariantMetadataGroup group) {
        return ResponseEntity.ok(variantManagementService.createGroup(group));
    }

    @PutMapping("/groups/{groupId}")
    public ResponseEntity<LCMMenuVariantMetadataGroupDomain> updateGroup(@PathVariable Long groupId, @RequestBody LCMMenuVariantMetadataGroupDomain group) {
        return ResponseEntity.ok(variantManagementService.updateGroup(groupId, group));
    }

    @DeleteMapping("/groups/{groupId}")
    public ResponseEntity<Void> deleteGroup(@PathVariable Long groupId) {
        variantManagementService.deleteGroup(groupId);
        return ResponseEntity.ok().build();
    }

    // Item endpoints
    @GetMapping("/items")
    public ResponseEntity<List<LCMMenuVariantMetadataItemDomain>> getItemsByGroup(@RequestParam Long groupId) {
        return ResponseEntity.ok(variantManagementService.getItemsByGroup(groupId));
    }

    @PostMapping("/items")
    public ResponseEntity<LCMMenuVariantMetadataItemDomain> createItem(@RequestBody LCMMenuVariantMetadataItem item , @RequestParam Long groupId    ) {
        return ResponseEntity.ok(variantManagementService.createItem(item,groupId));
    }

    @PutMapping("/items/{itemId}")
    public ResponseEntity<LCMMenuVariantMetadataItemDomain> updateItem(@PathVariable Long itemId, @RequestParam Long groupId , @RequestBody LCMMenuVariantMetadataItemDomain item) {
        return ResponseEntity.ok(variantManagementService.updateItem(itemId, item,groupId));
    }

    @DeleteMapping("/items/{itemId}")
    public ResponseEntity<Void> deleteItem(@PathVariable Long itemId) {
        variantManagementService.deleteItem(itemId);
        return ResponseEntity.ok().build();
    }
}