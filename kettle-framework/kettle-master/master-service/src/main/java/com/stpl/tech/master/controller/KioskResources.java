/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.KioskManagementService;
import com.stpl.tech.master.domain.model.KioskCompanyDetails;
import com.stpl.tech.master.domain.model.KioskLocationDetails;
import com.stpl.tech.master.domain.model.KioskOfficeDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.ws.rs.core.MediaType;
import java.util.List;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.KIOSK_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + KIOSK_MANAGEMENT_ROOT_CONTEXT)
public class KioskResources extends AbstractResources {

    @Autowired
    KioskManagementService kioskManagementService;

    /*
    @RequestMapping(value = "location-details", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON)
    public List<KioskLocationDetails> getMonkLocationDetails(){

    }*/

    @RequestMapping(value = "location-details", method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public KioskLocationDetails addLocationDetails(@RequestBody KioskLocationDetails locationDetails) throws DataNotFoundException {
        return kioskManagementService.saveLocationDetails(locationDetails);
    }

    @RequestMapping(value = "office-details", method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public KioskOfficeDetails addOfficeDetails(@RequestBody KioskOfficeDetails officeDetails) throws DataNotFoundException {
        return kioskManagementService.saveOfficeDetails(officeDetails);
    }

    @RequestMapping(value = "company-details", method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public KioskCompanyDetails addCompanyDetails(@RequestBody KioskCompanyDetails companyDetails){
        return kioskManagementService.saveCompanyDetails(companyDetails);
    }

    @RequestMapping(value = "assign-unit", method = RequestMethod.GET,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public KioskLocationDetails addKioskUnitDetails(@RequestParam Integer locationId, @RequestParam Integer unitId ){
        return kioskManagementService.assignUnit(locationId,unitId);
    }


    @RequestMapping(value = "location-details/update", method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public KioskLocationDetails updateLocationDetails(@RequestBody KioskLocationDetails locationDetails) throws DataNotFoundException {
        return kioskManagementService.updateLocationDetails(locationDetails);
    }

    @RequestMapping(value = "office-details/update", method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public KioskOfficeDetails updateOfficeDetails(@RequestBody KioskOfficeDetails officeDetails) throws DataNotFoundException {
        return kioskManagementService.updateOfficeDetails(officeDetails);
    }

    @RequestMapping(value = "company-details/update", method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public KioskCompanyDetails updateCompanyDetails(@RequestBody KioskCompanyDetails companyDetails){
        return kioskManagementService.updateCompanyDetails(companyDetails);
    }


    @RequestMapping(value = "location-details/change-status", method = RequestMethod.GET,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean activateLocationDetails(@RequestParam Integer locationId, @RequestParam boolean activate){
        return kioskManagementService.changeLocationStatus(locationId,null,null, activate);
    }

    @RequestMapping(value = "office-details/change-status", method = RequestMethod.GET,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean activateOfficeDetails(@RequestParam Integer officeId, @RequestParam boolean activate){
        return kioskManagementService.changeOfficeStatus(officeId,null, activate);
    }

    @RequestMapping(value = "company-details/change-status", method = RequestMethod.GET,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean activateCompanyDetails(@RequestParam Integer companyId, @RequestParam boolean activate){
        return kioskManagementService.changeCompanyStatus(companyId, activate);
    }


    @RequestMapping(value = "company-details", method = RequestMethod.GET,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<KioskCompanyDetails> getAllKioskCompanies(){
        return kioskManagementService.getCompanyDetailMap();
    }



}
