/**
 * 
 */
package com.stpl.tech.master.monk.helper;

import org.apache.commons.lang.builder.CompareToBuilder;

import com.stpl.tech.master.core.service.impl.RecipeHelper;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeData;

/**
 * <AUTHOR>
 *
 */
public class MonkRecipeKey implements Comparable<MonkRecipeKey> {

	private int productId;
	private String dimension;
	private int dimensionCode;
	private int quantity;

	public MonkRecipeKey(MonkRecipeData data) {
		this.productId = data.getProductId();
		this.dimension = data.getDimension();
		this.quantity = data.getQuantity();
		this.dimensionCode = RecipeHelper.getDimensionCode(data.getDimension());
	}

	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	public int getDimensionCode() {
		return dimensionCode;
	}

	public void setDimensionCode(int dimensionCode) {
		this.dimensionCode = dimensionCode;
	}

	public int getQuantity() {
		return quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Comparable#compareTo(java.lang.Object)
	 */
	@Override
	public int compareTo(MonkRecipeKey o) {
		return new CompareToBuilder().append(this.productId, o.productId).append(this.dimensionCode, o.dimensionCode)
				.append(this.quantity, o.quantity).toComparison();
	}

}
