package com.stpl.tech.master.service.impl;

import com.hazelcast.map.IMap;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.dao.WorkstationRoutingDao;
import com.stpl.tech.master.data.exception.WorkstationRoutingException;
import com.stpl.tech.master.data.model.RefLookupInfo;
import com.stpl.tech.master.data.model.Status;
import com.stpl.tech.master.data.model.UnitProductAsKey;
import com.stpl.tech.master.data.model.UnitProductStationMapping;
import com.stpl.tech.master.data.model.UnitStationCategoryMapping;
import com.stpl.tech.master.data.repository.UnitProductStationMappingRepository;
import com.stpl.tech.master.data.repository.UnitStationCategoryMappingRepository;
import com.stpl.tech.master.dto.WorkstationRoutingRequest;
import com.stpl.tech.master.dto.WorkstationRoutingResponse;
import com.stpl.tech.master.service.WorkstationRoutingService;
import com.stpl.tech.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class WorkstationRoutingServiceImpl implements WorkstationRoutingService {

    private static final String WORKSTATION_CACHE = "workstationRoutingCache";

    @Autowired
    private WorkstationRoutingDao workstationRoutingDao;

    @Autowired
    private UnitStationCategoryMappingRepository stationCategoryMappingRepository;

    @Autowired
    private UnitProductStationMappingRepository unitProductStationMappingRepository;
    
    @Autowired
    private MasterDataCache masterDataCache;
    
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public WorkstationRoutingResponse updateWorkstationRouting(WorkstationRoutingRequest request) {
        Assert.notNull(request, "Request cannot be null");
        Assert.notNull(request.getUnitId(), "Unit ID cannot be null");
        Assert.notNull(request.getRefLookupIdToProducts(), "RefLookupId to Products mapping cannot be null");
        
        log.info("Updating workstation routing for request: {}", request);
        
        // Validate refLookupId to Products mapping
        request.getRefLookupIdToProducts().forEach((refLookupId, productIds) -> {
            Assert.notNull(refLookupId, "RefLookupId cannot be null");
            Assert.notNull(productIds, "Product IDs list cannot be null");
            Assert.isTrue(!productIds.isEmpty(), "Product IDs list cannot be empty");
            Assert.isTrue(productIds.stream().noneMatch(Objects::isNull), "Product IDs cannot contain null values");
        });
        
        Map<Integer, List<Integer>> updatedProducts = new HashMap<>();
        Map<Integer, List<Integer>> existingMappings = workstationRoutingDao.getProductRefLookupMappings(request.getUnitId());
        
        // Process each station category and its products
        for (Map.Entry<Integer, List<Integer>> entry : request.getRefLookupIdToProducts().entrySet()) {
            Integer refLookupId = entry.getKey();
            List<Integer> productIds = entry.getValue();
            
            try {
                // Get or create station category mapping
                UnitStationCategoryMapping stationMapping = createStationCategoryMapping(request.getUnitId(), refLookupId);
                
                // Process each product in this station category
                for (Integer productId : productIds) {
                    try {
                        // Check if product is mapped to any refLookupId
                        boolean isProductMapped = existingMappings != null &&
                                                          existingMappings.values().stream()
                                                                  .flatMap(List::stream)
                                                                  .anyMatch(id -> id.equals(productId));
                        
                        if (!isProductMapped) {
                            // New product mapping
                            log.info("Creating new mapping for product: {} to refLookupId: {}", productId, refLookupId);
                            createNewProductMapping(productId, stationMapping);
                        } else {
                            // Find current refLookupId for this product
                            Integer currentRefLookupId = findCurrentRefLookupId(existingMappings, productId);
                            
                            if (currentRefLookupId != null && !currentRefLookupId.equals(refLookupId)) {
                                // Product moved to different refLookupId
                                log.info("Moving product: {} from refLookupId: {} to refLookupId: {}",
                                        productId, currentRefLookupId, refLookupId);
                                moveProductToNewRefLookupId(productId, stationMapping);
                            } else {
                                // Product already mapped to this refLookupId
                                log.info("Product: {} already mapped to refLookupId: {}, skipping", productId, refLookupId);
                            }
                        }
                    } catch (WorkstationRoutingException e) {
                        log.error("Error processing product mapping for productId: {} and refLookupId: {}",
                                productId, refLookupId, e);
                        throw e;
                    } catch (Exception e) {
                        log.error("Unexpected error processing product mapping for productId: {} and refLookupId: {}",
                                productId, refLookupId, e);
                        throw new WorkstationRoutingException("Failed to process product mapping", e);
                    }
                }
                
                updatedProducts.put(refLookupId, productIds);
            } catch (WorkstationRoutingException e) {
                log.error("Error processing station category mapping for refLookupId: {}", refLookupId, e);
                throw e;
            } catch (Exception e) {
                log.error("Unexpected error processing station category mapping for refLookupId: {}", refLookupId, e);
                throw new WorkstationRoutingException("Failed to process station category mapping", e);
            }
        }
        
        return WorkstationRoutingResponse.builder()
                       .unitId(request.getUnitId())
                       .updatedProducts(updatedProducts)
                       .updatedAt(AppUtils.getCurrentDate())
                       .build();
    }
    
    
    private Integer findCurrentRefLookupId(Map<Integer, List<Integer>> existingMappings, Integer productId) {
        return existingMappings.entrySet().stream()
            .filter(entry -> entry.getValue().contains(productId))
            .map(Map.Entry::getKey)
            .findFirst()
            .orElse(null);
    }

    private void createNewProductMapping(Integer productId, UnitStationCategoryMapping stationMapping) {
        UnitProductStationMapping mapping = new UnitProductStationMapping();
        mapping.setProductId(productId);
        mapping.setStationCategoryMapping(stationMapping);
        mapping.setStatus(Status.ACTIVE);
        workstationRoutingDao.saveProductRefLookupMapping(mapping);
    }

    private void moveProductToNewRefLookupId(Integer productId, UnitStationCategoryMapping stationMapping) {
        // Find existing active mapping for the product
        List<UnitProductStationMapping> existingActiveMappings = unitProductStationMappingRepository.findByUnitIdAndProductId(
                stationMapping.getUnitId(), productId);

        if (existingActiveMappings != null && !existingActiveMappings.isEmpty()) {
            // Mark existing active mappings as INACTIVE
            existingActiveMappings.forEach(oldMapping -> {
                oldMapping.setStatus(Status.INACTIVE);
                workstationRoutingDao.saveProductRefLookupMapping(oldMapping);
                log.info("Marked product: {} under old refLookupId as INACTIVE.", productId);
            });
        } else {
            log.warn("No active mapping found for product: {} when trying to move.", productId);
        }

        // Create new mapping
        createNewProductMapping(productId, stationMapping);
    }

    @Override
    public Map<Integer, List<Integer>> getAllProductStationMappingForUnit(Integer unitId) {
        Assert.notNull(unitId, "Unit ID cannot be null");
        log.info("Fetching all product station mappings for unit: {}", unitId);
        
        try {
            IMap<UnitProductAsKey, RefLookupInfo> productToWorkStationMapping = masterDataCache.getWorkstationRoutingCache();
            
//            if (productToWorkStationMapping == null || productToWorkStationMapping.isEmpty()) {
//                log.warn("Cache is empty or unavailable for unit: {}", unitId);
//                return new HashMap<>();
//            }
            
            Map<Integer, List<Integer>> stationCategoryToProductMappings =
                    workstationRoutingDao.getProductRefLookupMappings(unitId);
            
            if (stationCategoryToProductMappings == null || stationCategoryToProductMappings.isEmpty()) {
                log.warn("No product mappings found for unit: {}", unitId);
                return new HashMap<>();
            }
            
            log.info("Successfully retrieved {} station category mappings for unit: {}",
                    stationCategoryToProductMappings.size(), unitId);
            return stationCategoryToProductMappings;
            
        } catch (Exception e) {
            log.error("Error fetching product station mappings for unit: {} - {}", unitId, e.getMessage(), e);
            // Instead of throwing exception, return empty map for better API behavior
            return new HashMap<>();
        }
    }
    
    private UnitStationCategoryMapping createStationCategoryMapping(Integer unitId, Integer refLookupId) {
        // First check if mapping already exists
        Optional<UnitStationCategoryMapping> existingMapping = stationCategoryMappingRepository
            .findByUnitIdAndRefLookupIdAndStatus(unitId, refLookupId, Status.ACTIVE);
        
        if (existingMapping.isPresent()) {
            log.info("Found existing station category mapping for unit: {} and refLookupId: {}", unitId, refLookupId);
            return existingMapping.get();
        }

        // Create new mapping if none exists
        log.info("Creating new station category mapping for unit: {} and refLookupId: {}", unitId, refLookupId);
        UnitStationCategoryMapping mapping = new UnitStationCategoryMapping();
        mapping.setUnitId(unitId);
        mapping.setRefLookupId(refLookupId);
        mapping.setStatus(Status.ACTIVE);
        return stationCategoryMappingRepository.save(mapping);
    }
}