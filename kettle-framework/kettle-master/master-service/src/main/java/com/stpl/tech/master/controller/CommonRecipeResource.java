/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.ws.rs.core.MediaType;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.View;

import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.RecipeIterationService;
import com.stpl.tech.master.core.service.RecipeService;
import com.stpl.tech.master.core.service.impl.RecipeHelper;
import com.stpl.tech.master.recipe.model.RecipeCost;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.AppConstants;

public class CommonRecipeResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(CommonRecipeResource.class);

	@Autowired
	private RecipeService recipeService;
	
	@Autowired
	private RecipeIterationService recipeIterationService;

	@Autowired
	private RecipeHelper recipeHelper;

	@Autowired
	private MasterProperties props;

	@Autowired
	private MasterDataCache masterDataCache;

	protected View getRecipeCostView(String fileName, List<RecipeDetail> details) {
		List<RecipeCost> costs = new ArrayList<>();
		for (RecipeDetail detail : details) {
			if (detail.getProduct().getType() == AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE) {
				continue;
			}
			try {
				costs.add(calculateRecipeCost(detail));
			} catch (Exception e) {
				LOG.error("Error while calculating cost for recipe " + detail.getProduct().getName() + "["
						+ detail.getDimension().getName() + "]");
			}
		}
		return recipeHelper.getRecipeView(costs, details, fileName);
	}

	protected View getView(String fileName, List<RecipeDetail> details) {
		List<RecipeCost> costs = new ArrayList<>();
		for (RecipeDetail detail : details) {
			if (detail.getProduct().getType() == AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE) {
				continue;
			}
			try {
				RecipeCost cost = calculateRecipeCost(detail);
				costs.add(cost);
			} catch (Exception e) {
				LOG.error("Error while calculating cost for recipe " + detail.getProduct().getName() + "["
						+ detail.getDimension().getName() + "]");
			}
		}
		return recipeHelper.getRecipeCostView(costs, fileName);
	}

	protected RecipeCost calculateRecipeCost(RecipeDetail detail) {
		String endPoint = props.getSCMRecipeCostUrl();
		try {
			HttpResponse response = createPostRequest(endPoint, detail);
			return WebServiceHelper.convertResponse(response, RecipeCost.class);
		} catch (Exception e) {
			LOG.error("Error while creating web request to {}", endPoint, e);
		}
		return null;
	}

	private HttpResponse createPostRequest(String url, RecipeDetail object)
			throws ClientProtocolException, IOException {
		HttpPost requestObject = new HttpPost(url);
		String recipeData = WebServiceHelper.convertToString(object);
		HttpEntity httpEntity = new StringEntity(recipeData, AppConstants.CHARSET);
		requestObject.setHeader("Content-type", MediaType.APPLICATION_JSON.toString());
		requestObject.setEntity(httpEntity);
		return WebServiceHelper.postRequest(requestObject);
	}

	/**
	 * @return
	 */
	protected RecipeService getRecipeService() {
		return recipeService;
	}

	/**
	 * @return
	 */
	protected RecipeHelper getRecipeHelper() {
		return recipeHelper;
	}
	
	/**
	 * @return
	 */
	protected RecipeIterationService getRecipeIterationService() {
		return recipeIterationService;
	}

	public MasterDataCache getMasterDataCache() {
		return masterDataCache;
	}
}
