package com.stpl.tech.master.controller;


import com.stpl.tech.kettle.report.metadata.model.WarningMetadata;
import com.stpl.tech.master.budget.metadata.model.ExpenseMetadata;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.MasterMetadataService;
import com.stpl.tech.master.core.service.PartnerMenuMetadataService;
import com.stpl.tech.master.data.model.CategoryAttributes;
import com.stpl.tech.master.data.model.MenuExcelUploadEvent;
import com.stpl.tech.master.domain.model.AddLocationRequest;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.CategoryAttributesDomain;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.MenuExcelUploadResponse;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.dto.WorkstationRoutingRequest;
import com.stpl.tech.master.dto.WorkstationRoutingResponse;
import com.stpl.tech.master.lock.service.SyncLock;
import com.stpl.tech.master.service.WorkstationRoutingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.MASTER_METADATA_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + MASTER_METADATA_ROOT_CONTEXT) // 'v1/metadata'
public class MasterMetadataResource extends  AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(MasterMetadataResource.class);

    @Autowired
    private MasterMetadataService metaService;

    @Autowired
    private PartnerMenuMetadataService partnerMenuMetadataService;

    @Autowired
    private WorkstationRoutingService workstationRoutingService;
    
    @Autowired
    private SyncLock syncLock;

    @RequestMapping(method = RequestMethod.POST, value = "expense-detail", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ExpenseMetadata> getExpenseSheet(@RequestBody String type) {
        return metaService.getExpenseList(type);
    }

    @RequestMapping(method = RequestMethod.GET, value = "warning-reason-list", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<WarningMetadata> getWarningReason(@RequestParam("impactType") String type) {
        LOG.info("Request to Get warning metadata " + type);
        return metaService.getWarningReasonList(type);
    }

    @RequestMapping(method = RequestMethod.GET, value = "payment-modes", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<PaymentMode> getPaymentModes() {
        LOG.info("Request to Get payment modes");
        return metaService.getPaymentModes();
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-location", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Location addLocationDetail(@RequestBody AddLocationRequest locationDetail) {
        LOG.info("Request to add location ");
        return metaService.addLocation(locationDetail);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-category-attributes", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<CategoryAttributes> getCategoryAttributes() {
        LOG.info("Request to get category attributes");
        return metaService.getCategoryAttributes();
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-category-attributes", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean updateCategoryAttributes(@RequestBody Map<Integer,CategoryAttributesDomain> categoryAttributes, @RequestParam Boolean removeSequencing) {
        LOG.info("Request to update/add category attributes");
        return metaService.updateCategoryAttributes(categoryAttributes,removeSequencing);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-category-images", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean uploadCategoryImage(@RequestBody Map<Integer, CategoryAttributesDomain> categoryImages) throws Exception {
        LOG.info("Request to update/add category images");
        return metaService.uploadCategoryImage(categoryImages);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-menu-excel", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public MenuExcelUploadResponse uploadMenuExcel(@RequestParam(value = "file") final MultipartFile file , String selectedSheets , String menuApp, HttpServletRequest request) throws Exception {
        LOG.info("Request to upload Excel");
        Integer uploadedBy = getLoggedInUser(request);
        List<String> selectedSheetsList = Arrays.stream(selectedSheets.split(",")).map(sheet -> sheet.trim()).
                collect(Collectors.toList());
       MenuExcelUploadResponse response =   syncLock.syncLock(menuApp,100l,()->{
            try {
                return partnerMenuMetadataService.processExcelUpload(file,uploadedBy, selectedSheetsList , menuApp);
            } catch (DataUpdationException e) {
                throw new RuntimeException(e);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        },true);
       if(Objects.isNull(response)){
           return MenuExcelUploadResponse.builder().errorsList(new ArrayList<>(Arrays.asList("Another Sheet Processing In Process !! ." +
                   " Please Try Again After a minute"))).build();
       }
       return response;
    }

    @GetMapping("/menu-mapping-excel/download")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View downloadTemplate(@RequestParam String menuApp , @RequestParam Boolean isTemplate) {
        return partnerMenuMetadataService.downloadExcelForMenuMappings(menuApp,isTemplate);
    }

    @GetMapping("/menu-mapping-excel/event")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public MenuExcelUploadEvent downloadLatestEvent(@RequestParam String menuApp) {
        return partnerMenuMetadataService.getLatestMenuMappingUploadEvent(menuApp);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-company-brand-mapping")
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Map<Integer, List<Brand>> getCompanyBrandsMapping() {
        LOG.info("Request to get Company Brands Mapping");
        return metaService.getCompanyBrandsMapping();
    }

    @RequestMapping(method = RequestMethod.POST, value = "{unitId}/update-workstation-routing", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public WorkstationRoutingResponse updateWorkstationRouting(
            @PathVariable("unitId") Integer unitId,
            @RequestBody WorkstationRoutingRequest request) {
        LOG.info("Request to update workstation routing for unit: {}", unitId);
        return workstationRoutingService.updateWorkstationRouting(request);
    }
    
    @RequestMapping(method = RequestMethod.GET, value = "get-product-station-category-mapping", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ResponseEntity<Map<Integer, List<Integer>>> getProductRefLookupMapping(
            @RequestParam("unitId") Integer unitId) {
        
        LOG.info("Request to get product to refLookupId mapping for unit: {}", unitId);
        
        try {
            if (unitId == null || unitId <= 0) {
                LOG.warn("Invalid unitId provided: {}", unitId);
                return ResponseEntity.badRequest().build();
            }
            
            Map<Integer, List<Integer>> mappings = workstationRoutingService.getAllProductStationMappingForUnit(unitId);
            
            if (mappings.isEmpty()) {
                LOG.info("No mappings found for unit: {}", unitId);
                return ResponseEntity.ok(new HashMap<>());
            }
            
            LOG.info("Successfully retrieved mappings for unit: {}", unitId);
            return ResponseEntity.ok(mappings);
            
        } catch (Exception e) {
            LOG.error("Error processing request for unit: {} - {}", unitId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
