/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.dao.RecipeIterationDao;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.recipe.calculator.model.IterationIngredientInstructions;
import com.stpl.tech.master.recipe.calculator.model.RecipeIterationDetail;
import com.stpl.tech.master.recipe.calculator.model.RecipeIterationStatus;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SCM_RECIPE_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + SCM_RECIPE_SERVICES_ROOT_CONTEXT)
public class SCMRecipeResource extends CommonRecipeResource {

	private static final Logger LOG = LoggerFactory.getLogger(SCMRecipeResource.class);

	@Autowired
	private RecipeIterationDao recipeIterationDao;

	@Autowired
	private MasterDataCache masterDataCache;

	/**
	 * Find all recipes
	 *
	 * @return
	 * @throws DataNotFoundException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "findAll", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<RecipeDetail> findAllSCMRecipes() throws DataNotFoundException {
		LOG.info("Find All SCM recipes for all product and dimesions");
		return getRecipeService().findAllSCM();
	}

	@RequestMapping(method = RequestMethod.POST, value = "update-recipe-ingredients-by-conversion", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<RecipeDetail> updateRecipeIngredientsWithConversionRatios(@RequestBody Map<Integer, Map<Integer, BigDecimal>> productConvesrionMap) throws DataNotFoundException {
		LOG.info("Find All SCM recipes for all recipes in : {}", productConvesrionMap);
		return getRecipeService().applyConversionRatiosToRecipeIngredients(productConvesrionMap);
	}

	@RequestMapping(method = RequestMethod.POST, value = "findContainigName", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<RecipeDetail> findRecipes(@RequestParam final String name) throws DataNotFoundException {
		LOG.info("Find SCM recipes for product having name like " + name);
		return getRecipeService().findSCMRecipesContainingName(name);
	}

	@RequestMapping(method = RequestMethod.GET, value = "findAllBasic", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<IdName> findAllSCMRecipesBasic() throws DataNotFoundException {
		LOG.info("Find All SCM recipes for all product and dimesions");
		List<IdName> list = new ArrayList<>();
		List<RecipeDetail> recipes = getRecipeService().findAllSCM();
		recipes = getRecipeService().filterDetailsByCompanyAndBrand(recipes);
		if (recipes != null) {
			for (RecipeDetail recipe : recipes) {
				IdName idName = new IdName();
				idName.setId(recipe.getProduct().getProductId());
				idName.setName(recipe.getName());
				list.add(idName);
			}
		}
		return list;
	}

	/**
	 * Download all recipes
	 *
	 * @return View
	 */
	@RequestMapping(method = RequestMethod.POST, value = "get-all-recipe-dump")
	public View getAllSCMRecipeViewExcel() {
		List<RecipeDetail> details = getRecipeService().findAllSCM();
		details = getRecipeService().filterDetailsByCompanyAndBrand(details);
		String fileName = "\"All SCM Recipe Detail - " + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xls\"";
		return getRecipeHelper().getRecipeDetailView(details, fileName);
	}

	/**
	 * Download all recipes
	 *
	 * @return View
	 */
	@RequestMapping(method = RequestMethod.POST, value = "get-all-recipe-instruction-dump")
	public View getAllSCMRecipeInstructionsViewExcel() {
		List<RecipeDetail> details = getRecipeService().findAllSCM();
		String fileName = "\"All SCM Recipe Instruction Detail - " + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xls\"";
		return getRecipeHelper().getRecipeInstructionDetailView(details, fileName);

	}

	/**
	 * Download all recipes with cost
	 *
	 * @return View
	 */
	@RequestMapping(method = RequestMethod.POST, value = "get-all-recipe-with-cost-dump")
	public View getAllSCMRecipeWithCostViewExcel() {
		List<RecipeDetail> details = getRecipeService().findAllSCM();
		return getRecipeCostView(
				"\"All SCM Recipe Detail With Cost- " + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xls\"",
				details);

	}

	/**
	 * Download all recipes
	 *
	 * @return View
	 */
	@RequestMapping(method = RequestMethod.POST, value = "get-all-recipe-cost-dump")
	public View getAllSCMRecipeCostViewExcel() {
		List<RecipeDetail> details = getRecipeService().findAllSCM();
		return getView("\"All SCM Recipe Cost Detail - " + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xls\"",
				details);
	}

	@RequestMapping(method = RequestMethod.POST, value = "addIteration", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public RecipeIterationDetail addSCMRecipe(@RequestBody final RecipeIterationDetail recipeIterationDetail)
			throws DataNotFoundException {
		LOG.info("Adding recipe iteration : ", recipeIterationDetail.getIterationName());
		RecipeIterationDetail detail = getRecipeIterationService().addRecipeIteration(recipeIterationDetail);
		// getRecipeHelper().notify(getRecipeHelper().getRecipeAddTemplate(detail));
		return detail;
	}

	@RequestMapping(method = RequestMethod.POST, value = "updateIteration", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public int updateRecipe(@RequestBody final RecipeIterationDetail recipeIterationDetail)
			throws DataNotFoundException {
		LOG.info("Updating recipe iteration : ", recipeIterationDetail.getIterationId());
		int recipeId = getRecipeIterationService().updateRecipeIeration(recipeIterationDetail);
		// getRecipeHelper().notify(getRecipeHelper().getRecipeUpdateTemplate(detail));
		return recipeId;
	}

	@RequestMapping(method = RequestMethod.POST, value = "iterationForProduct", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<RecipeIterationDetail> getIterationForProduct(@RequestBody final int productId, @RequestParam boolean checkDecomissoned) {
		List<RecipeIterationDetail> detailList = getRecipeIterationService().getIterationForProduct(productId);
		List<RecipeIterationDetail> decomissionedList = new ArrayList();
		if(!checkDecomissoned) {
			for (RecipeIterationDetail rt : detailList) {
				if (!rt.getStatus().equals(RecipeIterationStatus.DECOMISSONED)) {
					decomissionedList.add(rt);
				}
			}
			return decomissionedList;
		}
		else
		return detailList;

	}

	@RequestMapping(method = RequestMethod.POST, value = "iterationForConstruct", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<RecipeIterationDetail> getIterationForConstruct(@RequestParam("constructName") String constructName) {
		List<RecipeIterationDetail> detailList = getRecipeIterationService().getIterationForConstruct(constructName);
		return detailList;

	}

	@RequestMapping(method = RequestMethod.GET, value = "validateConstructName", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean validateConstructName(@RequestParam("constructName") String constructName) {
		return getRecipeIterationService().validateConstructName(constructName);
	}

	@RequestMapping(method = RequestMethod.GET, value = "validateIterationName", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean validateIterationName(@RequestParam("iterationName") String iterationName) {
		return getRecipeIterationService().validateIterationName(iterationName);
	}

	@RequestMapping(method = RequestMethod.GET, value = "archivedIterationConstruct", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<RecipeIterationDetail> getArchivedIterationForConstruct(@RequestParam("constructName") String constructName) {
		List<RecipeIterationDetail> detailList = getRecipeIterationService().getArchivedIterationForConstruct(constructName);
		return detailList;

	}

	@RequestMapping(method = RequestMethod.GET, value = "archivedIterationProduct", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<RecipeIterationDetail> getArchivedIterationForProduct(@RequestParam("productId") int productId) {
		List<RecipeIterationDetail> detailList = getRecipeIterationService().getArchivedIterationForProduct(productId);
		return detailList;

	}

	@RequestMapping(method = RequestMethod.GET, value = "allConstruct", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Map<String,Object> getAllSCMConstruct() {
		Map<String,Object> constructMap = getRecipeIterationService().getAllSCMConstruct();
		return constructMap;
	}

	@RequestMapping(method = RequestMethod.POST, value = "updateIterationStatus", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean changeIterationStatus(@RequestBody RecipeIterationDetail recipeIterationDetail) throws DataNotFoundException, EmailGenerationException, IllegalAccessException {
		return getRecipeIterationService().changeIterationStatus(recipeIterationDetail);
	}

	@RequestMapping(method = RequestMethod.POST, value = "updateIterationsComment", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean changeIterationComment(@RequestBody RecipeIterationDetail recipeIterationDetail) throws DataNotFoundException {
		return getRecipeIterationService().changeIterationComment(recipeIterationDetail);
	}

	@RequestMapping(method = RequestMethod.POST, value = "uploadSCMrecipemedia",
			produces = MediaType.APPLICATION_JSON, consumes = MediaType.MULTIPART_FORM_DATA)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Boolean uploadSCMRecipeMedia(HttpServletRequest request,
										@RequestParam(value = "recipeIteration_id") String id,
										@RequestParam(value = "file") final MultipartFile [] file) throws DataNotFoundException {
		return getRecipeIterationService().updateIterationImage(id,file);
	}




	@RequestMapping(method = RequestMethod.POST, value = "addIngredientInstructions", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean addIngredientInstructions(@RequestBody Set<String> instructions) throws DataNotFoundException {
		return getRecipeIterationService().addIngredientInstructions(instructions);
	}

	@RequestMapping(method = RequestMethod.GET, value = "getAllIngredientInstructions", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<IterationIngredientInstructions> getAllIngredientInstructions() throws DataNotFoundException {
		return getRecipeIterationService().getAllIngredientInstructions();
	}

	@RequestMapping(method = RequestMethod.POST, value = "check-recipe-for-sm-products", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<String> checkRecipeForSmProducts(@RequestBody List<Integer> productIds) throws DataNotFoundException {
		return Collections.singletonList(getRecipeIterationService().checkRecipeForSmProducts(productIds));
	}



}
