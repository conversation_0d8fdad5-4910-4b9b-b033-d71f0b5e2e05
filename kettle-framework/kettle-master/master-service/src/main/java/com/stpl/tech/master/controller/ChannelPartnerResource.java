package com.stpl.tech.master.controller;


import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.CHANNEL_PARTNER_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;

import com.stpl.tech.master.core.external.partner.dao.ChannelPartnerDao;
import com.stpl.tech.master.domain.model.ChangeMenuStatusRequest;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.domain.RequestContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.AbstractExceptionHandler;
import com.stpl.tech.master.core.service.ChannelPartnerService;
import com.stpl.tech.master.core.service.MasterDataCacheService;
import com.stpl.tech.master.data.model.ChannelPartner;
import com.stpl.tech.master.data.model.MenuSequenceMappingData;
import com.stpl.tech.master.data.model.ProductGroupData;
import com.stpl.tech.master.data.model.ProductGroupImageData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMappingData;
import com.stpl.tech.master.domain.model.ChannelPartnerDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdIndex;
import com.stpl.tech.master.domain.model.MenuApp;
import com.stpl.tech.master.domain.model.MenuRecommendation;
import com.stpl.tech.master.domain.model.MenuSequence;
import com.stpl.tech.master.domain.model.MenuSequenceRequestVO;
import com.stpl.tech.master.domain.model.MenuType;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.PriceProfileDetail;
import com.stpl.tech.master.domain.model.PriceProfileStrategy;
import com.stpl.tech.master.domain.model.ProductGroup;
import com.stpl.tech.master.domain.model.UnitChannelPartnerMapping;
import com.stpl.tech.master.domain.model.UnitMenuMappingKeys;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.domain.model.UnitPartnerMenuMapping;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + CHANNEL_PARTNER_ROOT_CONTEXT) // 'v1/channel-partner'
public class ChannelPartnerResource extends AbstractExceptionHandler {

    private static final Logger LOG = LoggerFactory.getLogger(ChannelPartnerResource.class);

    @Autowired
    private ChannelPartnerService channelPartnerService;

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    private MasterDataCacheService masterDataCacheService;

    @Autowired
    private MasterProperties props;

    @Autowired
    ChannelPartnerDao dao;

    /////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////// Channel Partner APIs ////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////


    @RequestMapping(method = RequestMethod.GET, value = "channel-partner", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<IdCodeName> getAllChannelPartners() {
        LOG.info("Request to get all Channel Partners");
        return channelPartnerService.getAllChannelPartners(AppUtils.getBusinessDate());
    }

    @RequestMapping(method = RequestMethod.POST, value = "channel-partner/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public IdCodeName updateChannelPartner(@RequestBody final ChannelPartner channelPartner)
        throws DataNotFoundException {
        LOG.info("Request to update Channel Partner " + channelPartner.getPartnerDisplayName());
        return channelPartnerService.updateChannelPartner(channelPartner, AppUtils.getBusinessDate());
    }


    @RequestMapping(method = RequestMethod.GET, value = "/menu-type", produces = MediaType.APPLICATION_JSON)
    public Object[] getMenuType() {
        LOG.info("Request to get menu types");
        return MenuType.values();
    }

    @RequestMapping(method = RequestMethod.GET, value = "channel-partner/active", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ChannelPartnerDetail> getActiveChannelPartners() {
        LOG.info("Request to get active Channel Partners");
        return masterCache.getAllChannelPartners().stream().filter(channelPartnerDetail ->
            AppConstants.ACTIVE.equalsIgnoreCase(channelPartnerDetail.getStatus())).collect(Collectors.toList());
    }


    /////////////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////// Unit Channel Partner Mapping APIs ////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////


    @RequestMapping(method = RequestMethod.GET, value = "unit-channel-partner", produces = MediaType.APPLICATION_JSON)
    public List<UnitChannelPartnerMapping> getUnitChannelPartnerMappings() {
        List<UnitChannelPartnerMapping> mappings = masterCache.getActiveUnitChannelPartnerMapping();
        if (mappings == null) {
            mappings = new ArrayList<>();
        }
        return mappings;
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit-channel-partner/get", produces = MediaType.APPLICATION_JSON)
    public List<UnitChannelPartnerMapping> getAllUnitChannelPartnerMappings() {
        List<UnitChannelPartnerMapping> mappings = new ArrayList<>();
        for (UnitChannelPartnerMappingData data : channelPartnerService.getAllUnitChannelPartnerMappings()) {
            UnitChannelPartnerMapping mapping = new UnitChannelPartnerMapping();
            mapping.setId(data.getId());
            //mapping.setDeliveryPartner(metadataCache.getDeliveryPartner(data.getDeliveryPartnerId()));
            mapping.setChannelPartner(masterCache.getChannelPartner(data.getChannelPartnerId()));
                mapping.setUnit(new IdCodeName(data.getUnitId(), masterCache.getUnit(data.getUnitId()).getName(), ""));
                mapping.setStatus(data.getStatus());
                mappings.add(mapping);
        }
        if (RequestContext.isContextAvailable()) {
            List<Integer> mappedUnits = MasterUtil.getMappedUnits();
            mappings = mappings.stream().filter(m -> mappedUnits.contains(m.getUnit().getId()))
                    .collect(Collectors.toList());
        }

        return mappings;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-channel-partner/add", produces = MediaType.APPLICATION_JSON)
    public Boolean addUnitChannelPartnerMappings(@RequestBody UnitChannelPartnerMapping mapping)
            throws DataUpdationException {
        UnitChannelPartnerMappingData data = channelPartnerService.addUnitChannelPartnerMapping(mapping);
        if (Objects.nonNull(data)) {
            masterDataCacheService.loadUnitChannelPartnerMapping(data);
            return true;
        }
        return false;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-channel-partner/activate", produces = MediaType.APPLICATION_JSON)
    public Boolean activateUnitChannelPartnerMapping(@RequestBody Integer mappingId) {
        UnitChannelPartnerMappingData data = channelPartnerService.activateUnitChannelPartnerMapping(mappingId, true);
        if (Objects.nonNull(data)) {
            masterDataCacheService.loadUnitChannelPartnerMapping(data);
            return true;
        }
        return false;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-channel-partner/deactivate", produces = MediaType.APPLICATION_JSON)
    public Boolean deactivateUnitChannelPartnerMapping(@RequestBody Integer mappingId) {
        UnitChannelPartnerMappingData data = channelPartnerService.activateUnitChannelPartnerMapping(mappingId, false);
        if (Objects.nonNull(data)) {
            masterDataCacheService.loadUnitChannelPartnerMapping(data);
            return true;
        }
        return false;
    }


    /////////////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////// Unit Channel Partner Menu Mapping APIs ///////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////


    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-menu/add", produces = MediaType.APPLICATION_JSON)
    public UnitPartnerMenuMapping addUnitPartnerMenuMapping(@RequestBody UnitPartnerMenuMapping mapping)
            throws DataUpdationException {
        UnitPartnerMenuMapping unitPartnerMenuMapping = channelPartnerService.addUnitPartnerMenuMapping(mapping, false);
        // masterDataCacheService.loadUnitChannelPartnerMapping();
        if (unitPartnerMenuMapping != null) {
            masterDataCacheService.loadUnitPartnerMenuMapping(mapping);
        }
        return unitPartnerMenuMapping;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-menu/add-bulk", produces = MediaType.APPLICATION_JSON)
    public List<UnitPartnerMenuMapping> addUnitPartnerMenuMappingBulk(@RequestBody List<UnitPartnerMenuMapping> mapping) throws DataUpdationException {
        List<UnitPartnerMenuMapping> result = new ArrayList<>();
        List<Integer> menuSequenceIds = new ArrayList<>();
        for (UnitPartnerMenuMapping data : mapping) {
            menuSequenceIds.add(data.getMenuSequence().getId());
        }
        List<String> menuSequenceNamesList = dao.findAllNotActiveMenuSequenceData(menuSequenceIds);
        if (!menuSequenceNamesList.isEmpty()) {
            throw new DataUpdationException("Could Not Add Menu Mapping as Menu Sequences " +
                    String.join(",", menuSequenceNamesList) + " are either IN_ACTIVE or ARCHIVED");
        }

        for(UnitPartnerMenuMapping data : mapping) {
            UnitPartnerMenuMapping unitPartnerMenuMapping = channelPartnerService.addUnitPartnerMenuMapping(data, true);
            if(unitPartnerMenuMapping!=null){
                result.add(unitPartnerMenuMapping);
                masterDataCacheService.loadUnitPartnerMenuMapping(unitPartnerMenuMapping);
            }
        }
        return result;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-menu/get", produces = MediaType.APPLICATION_JSON)
    public List<UnitPartnerMenuMapping> getAllUnitPartnerMenuMapping(@RequestBody UnitPartnerBrandKey request) throws DataNotFoundException {
        List<UnitPartnerMenuMapping> mappings = new ArrayList<>();
        if (request == null || request.getBrandId() == null || request.getPartnerId() == null) {
            throw new DataNotFoundException("Partner id or brand id is not valid");
        }
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        Collection<UnitChannelPartnerMapping> channelPartnerMappingData = masterCache.getAllUnitChannelPartnerMapping();
        LOG.info("Getting Unit Channel Partner Mapping ends ::: {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        Map<Integer, UnitChannelPartnerMapping> unitChannelPartnerMappingValue = new HashMap<>();
        channelPartnerMappingData.forEach(unitChannelPartnerMapping -> {
            unitChannelPartnerMappingValue.put(unitChannelPartnerMapping.getId(), unitChannelPartnerMapping);
        });
        LOG.info("Created Map for Unit Channel Partner Mapping::: {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        List<UnitPartnerMenuMapping> unitPartnerMenuMappingList= masterCache.getUnitPartnerMenuMappings(request.getBrandId());
        LOG.info("Getting Unit Channel Partner Menu Mapping::: {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        for (UnitPartnerMenuMapping data : unitPartnerMenuMappingList) {
            if (request.getBrandId().equals(data.getBrand().getId())) {
                UnitPartnerMenuMapping mapping = new UnitPartnerMenuMapping();
                if (unitChannelPartnerMappingValue.containsKey(data.getUnitChannelPartnerMappingId())) {
                    UnitChannelPartnerMapping unitChannelPartnerMapping = unitChannelPartnerMappingValue.get(data.getUnitChannelPartnerMappingId());
                    mapping.setChannelPartner(unitChannelPartnerMapping.getChannelPartner());
                    mapping.setUnit(unitChannelPartnerMapping.getUnit());
                    mapping.setUnitChannelPartnerMappingId(unitChannelPartnerMapping.getId());
                }
                if (!request.getPartnerId().equals(mapping.getChannelPartner().getId())) {
                    continue;
                }
                mapping.setUpdatedBy(data.getUpdatedBy());
                mapping.setUpdatedAt(data.getUpdatedAt());
                mapping.setStatus(data.getStatus());
                mapping.setStartTime(data.getStartTime());
                mapping.setMenuType(data.getMenuType());
                mapping.setMenuSequence(data.getMenuSequence());
                mapping.setMenuApp(data.getMenuApp());
                mapping.setId(data.getId());
                mapping.setEndTime(data.getEndTime());
                mapping.setDayNumber(data.getDayNumber());
                mapping.setMenuRecommendationSequenceId(data.getMenuRecommendationSequenceId());
                mapping.setPriceProfileId(data.getPriceProfileId());
                mapping.setCartRecommendationSequenceId(data.getCartRecommendationSequenceId());
                mapping.setCreatedBy(data.getCreatedBy());
                mapping.setCreatedAt(data.getCreatedAt());
                mapping.setBrand(data.getBrand());
                mappings.add(mapping);
            }
        }
        LOG.info("Processing Done ::: {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
        return mappings;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-menu/activate", produces = MediaType.APPLICATION_JSON)
    public Boolean activateUnitPartnerMenuMapping(@RequestBody Integer mappingId) throws DataUpdationException {
        if (channelPartnerService.activateUnitPartnerMenuMapping(mappingId, true)){
            return true;
        }
        return false;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-menu/status-change-bulk", produces = MediaType.APPLICATION_JSON)
    public Boolean activateUnitPartnerMenuMapping(@RequestBody UnitMenuMappingKeys unitMenuMappingKeys) {
        if (channelPartnerService.bulkActivateUnitPartnerMenuMapping(unitMenuMappingKeys.getMappingIds(), unitMenuMappingKeys.getStatus())){
            //masterDataCacheService.loadUnitChannelPartnerMapping();
            masterDataCacheService.loadUnitPartnerMenuMapping();
            return true;
        }
        return false;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-menu/deactivate", produces = MediaType.APPLICATION_JSON)
    public Boolean deactivateUnitPartnerMenuMapping(@RequestBody Integer mappingId) throws DataUpdationException {
        if (channelPartnerService.activateUnitPartnerMenuMapping(mappingId, false)) {
            return true;
        }
        return false;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-recommendation-mapping", produces = MediaType.APPLICATION_JSON)
    public Boolean unitRecommendationMapping(@RequestBody List<IdIndex> list) {
        return channelPartnerService.unitRecommendationMapping(list);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-partner-price-profile-mapping", produces = MediaType.APPLICATION_JSON)
    public Boolean unitRecommendationPriceProfileMapping(@RequestBody List<IdIndex> list) {
        return channelPartnerService.unitRecommendationPriceProfileMapping(list);
    }


    ///////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////  Channel Partner Menu Generation APIs ////////////////////////
    ///////////////////////////////////////////////////////////////////////////////////////////


    @RequestMapping(method = RequestMethod.GET, value = "menu-app", produces = MediaType.APPLICATION_JSON)
    public Object[] getMenuApps() {
        LOG.info("Request to get menu app");
        return MenuApp.values();
    }



    @RequestMapping(method = RequestMethod.POST, value = "menu-sequence-get", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public MenuSequence getMenuSequence(@RequestBody MenuSequenceRequestVO request) throws DataNotFoundException {
        LOG.info("Request to get partner menu sequence: " + new Gson().toJson(request));
        return channelPartnerService.getMenuSequence(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "groups/create", produces = MediaType.APPLICATION_JSON)
    public List<ProductGroup> createProductGroups(@RequestBody List<ProductGroup> request) throws DataUpdationException {
        LOG.info("Request to create product groups");
        return channelPartnerService.createProductGroups(request);
    }

    @RequestMapping(method = RequestMethod.GET, value = "groups/get/menu-app", produces = MediaType.APPLICATION_JSON)
    public List<ProductGroup> getProductGroupsByFilter(@RequestParam String groupType, @RequestParam String menuAppType) {
        LOG.info("Request to get FILTERED groups");
        return channelPartnerService.getProductGroupsByFilter(groupType, menuAppType);
    }

    @RequestMapping(method = RequestMethod.GET, value = "groups/get", produces = MediaType.APPLICATION_JSON)
    public List<ProductGroup> getProductGroups() {
        LOG.info("Request to get ALL groups");
        return channelPartnerService.getProductGroups();
    }

    @RequestMapping(method = RequestMethod.GET, value = "getGroupMapping", produces = MediaType.APPLICATION_JSON)
    public List<MenuSequenceMappingData> getGroupMapping() {
        LOG.info("Request to get groups mapping");
        return channelPartnerService.getGroupMapping();
    }

    @RequestMapping(method = RequestMethod.POST, value = "groups/update", produces = MediaType.APPLICATION_JSON)
    public ProductGroupData updateGroup(@RequestBody ProductGroup productGroup) throws DataUpdationException {
        LOG.info("Request to update group");
        return channelPartnerService.updateGroup(productGroup);
    }

    @Deprecated
    @RequestMapping(method = RequestMethod.POST, value = "groups/product/sequence/create", produces = MediaType.APPLICATION_JSON)
    public List<ProductGroup> mapProductSequence(@RequestBody List<ProductGroup> request) throws DataUpdationException {
        LOG.info("Request to get product groups");
        return channelPartnerService.mapProductSequence(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "group/product/sequence/update", produces = MediaType.APPLICATION_JSON)
    public List<ProductGroup> updateProductSequence(@RequestBody List<ProductGroup> request) throws DataUpdationException {
        LOG.info("Request to get product groups");
        return channelPartnerService.updateProductSequence(request);
    }

    @RequestMapping(method = RequestMethod.GET, value = "menus/get", produces = MediaType.APPLICATION_JSON)
    public List<MenuSequence> getMenus() throws DataUpdationException {
        LOG.info("Request to get menus");
        return channelPartnerService.getMenus();
    }

    @RequestMapping(method = RequestMethod.GET, value = "menus/get/short", produces = MediaType.APPLICATION_JSON)
    public List<MenuSequence> getMenusShort(@RequestParam(value = "menu-status", required = false) String menuStatus) throws DataUpdationException {
        LOG.info("Request to get menus short");
        return channelPartnerService.getMenusShort(menuStatus);
    }

    @RequestMapping(method = RequestMethod.POST, value = "change-menu-status", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public Boolean changeMenuStatus(@RequestBody ChangeMenuStatusRequest request) throws DataUpdationException {
        LOG.info("Request to Update Menu Status for Menu Sequence Id {} to {}", request.getMenuSeqId(), request.getStatus());
        return channelPartnerService.changeMenuStatus(request.getMenuSeqId(), request.getStatus());
    }

    @RequestMapping(method = RequestMethod.POST, value = "menu/create", produces = MediaType.APPLICATION_JSON)
    public MenuSequence createMenu(@RequestBody MenuSequence request) throws DataUpdationException {
        LOG.info("Request to create menu");
        return channelPartnerService.createMenu(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "menu/sequence/create", produces = MediaType.APPLICATION_JSON)
    public MenuSequence createMenuSequence(@RequestBody MenuSequence request) throws DataUpdationException {
        LOG.info("Request to add menu sequence");
        return channelPartnerService.addMenuSequenceMapping(request);
    }

    @RequestMapping(method = RequestMethod.GET, value = "menu/get", produces = MediaType.APPLICATION_JSON)
    public MenuSequence getMenuForSequenceId(@RequestParam Integer menuSequenceId) throws DataUpdationException {
        LOG.info("Request to get menu for sequence Id: {}", menuSequenceId);
        return channelPartnerService.getMenuForSequenceId( menuSequenceId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unitIds/get", produces = MediaType.APPLICATION_JSON)
    public List<Integer> getUnitIdsForMenuSequence(@RequestParam(value = "menuSequenceId") Integer menuSequenceId, @RequestParam(value = "kettlePartnerId") Integer kettlePartnerId) throws DataUpdationException {
        return channelPartnerService.getUnitIdsForMenuSequence(menuSequenceId, kettlePartnerId);
    }

    ///////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////  Channel Partner Category Icon Upload API ////////////////////////
    ///////////////////////////////////////////////////////////////////////////////////////////

    @RequestMapping(method = RequestMethod.POST, value = "upload-icon", consumes = MediaType.MULTIPART_FORM_DATA)
    public ProductGroupImageData uploadProductImage(HttpServletRequest request,
                                                    @RequestParam(value = "mimeType") MimeType mimeType,
                                                    @RequestParam(value = "iconName") String iconName,
                                                    @RequestParam(value = "iconDescription") String iconDescription,
                                                    @RequestParam(value = "file") final MultipartFile file,
                                                    @RequestParam(value = "createdBy") int createdBy) throws DataUpdationException {
        return channelPartnerService.saveIcon(mimeType, iconName, iconDescription, file, createdBy, props.getIconHostUrl());
    }


    @RequestMapping(method = RequestMethod.GET, value = "icons", produces = MediaType.APPLICATION_JSON)
    public List<ProductGroupImageData> getIcons() {
        LOG.info("Request to get icons");
        return channelPartnerService.getIcons();
    }

    ///////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////  Channel Partner Category Recommendations API ////////////////////////
    ///////////////////////////////////////////////////////////////////////////////////////////

    @RequestMapping(method = RequestMethod.GET, value = "recommendation/get", produces = MediaType.APPLICATION_JSON)
    public List<MenuRecommendation> getRecommendation() {
        LOG.info("Request to get recommendation");
        return channelPartnerService.getRecommendation();
    }

    @RequestMapping(method = RequestMethod.POST, value = "recommendation/create", produces = MediaType.APPLICATION_JSON)
    public MenuRecommendation createRecommendation(@RequestBody MenuRecommendation request) throws DataUpdationException {
        LOG.info("Request to create recommendation");
        return channelPartnerService.createRecommendation(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "recommendation/mapping", produces = MediaType.APPLICATION_JSON)
    public Boolean createRecommendationMapping(@RequestBody MenuRecommendation request) throws DataUpdationException {
        LOG.info("Request to add recommendation sequence"+request);
        return channelPartnerService.createRecommendationMapping(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "recommendation/status", produces = MediaType.APPLICATION_JSON)
    public Boolean updateRecommendationStatus(@RequestBody IdCodeName request) throws DataUpdationException {
        LOG.info("Request to update recommendation status"+request);
        return channelPartnerService.updateRecommendationStatus(request.getId(),request.getStatus());
    }


    @RequestMapping(method = RequestMethod.POST, value = "upload-category-image", consumes = MediaType.MULTIPART_FORM_DATA)
    public IdCodeName uploadCategoryImage(HttpServletRequest request,
                                                    @RequestParam(value = "file") final MultipartFile file
                                                    ) throws DataUpdationException {
        return channelPartnerService.saveCategoryImage(file,props.getCategoryHostUrl());
    }


    @RequestMapping(method = RequestMethod.GET, value = "unit-partner-menu/get-profile-price", produces = MediaType.APPLICATION_JSON)
    public PriceProfileDetail getUnitPartnerProfilePrice(@RequestParam Integer priceProfileId) {
        return channelPartnerService.getUnitPartnerProfilePrice(priceProfileId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-all-profile-price", produces = MediaType.APPLICATION_JSON)
    public List<PriceProfileDetail> getUnitPartnerProfilePrice() {
        return channelPartnerService.getUnitPartnerProfilePrice();
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-price-profile-strategy", produces = MediaType.APPLICATION_JSON)
    public PriceProfileStrategy[] getProfilePriceStrategy() {
        return PriceProfileStrategy.values();
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-price-profile-mapping", produces = MediaType.APPLICATION_JSON)
    public boolean addProfilePriceMapping(@RequestBody PriceProfileDetail detail) {
        return channelPartnerService.addProfilePriceMapping(detail);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-price-profile-mapping", produces = MediaType.APPLICATION_JSON)
    public boolean updateProfilePriceMapping(@RequestBody IdCodeName detail) {
        return channelPartnerService.updateProfilePriceMapping(detail);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-price-profile", produces = MediaType.APPLICATION_JSON)
    public List<PriceProfileDetail> getProfilePrice(@RequestParam String priceProfileType){
        return channelPartnerService.getProfilePrice(priceProfileType);
    }


}
