/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.service.config;

import com.stpl.tech.master.core.config.KettleInterceptorConfig;
import com.stpl.tech.master.core.config.MasterConfig;
import com.stpl.tech.master.core.config.MasterExternalConfig;
import com.stpl.tech.master.core.config.MasterHazelcastConfig;
import com.stpl.tech.master.service.core.MasterServiceConstants;
import com.stpl.tech.spring.config.MasterSecurityConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

import java.util.TimeZone;

@SpringBootApplication
@EnableWebMvc
@EnableAutoConfiguration
@EnableTransactionManagement
@ComponentScan({ "com.stpl.tech.master" ,"com.stpl.tech.spring.crypto"})
@EnableScheduling
@Import(value = {MasterHazelcastConfig.class,MasterConfig.class,MasterExternalConfig.class,
		KettleInterceptorConfig.class, MasterSecurityConfiguration.class })
public class MasterServiceConfig extends SpringBootServletInitializer {

	static {
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
	}

	public static void main(String[] args) {
		SpringApplication.run(MasterServiceConfig.class, args);
	}

	@Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(MasterServiceConfig.class);
    }
	

	@Bean(name = "multipartResolver")
	public CommonsMultipartResolver commonsMultipartResolver() {
		CommonsMultipartResolver commonsMultipartResolver = new CommonsMultipartResolver();
		commonsMultipartResolver.setDefaultEncoding(MasterServiceConstants.CHARSET);
		commonsMultipartResolver.setMaxUploadSize(209715200);
		return commonsMultipartResolver;
	}
}