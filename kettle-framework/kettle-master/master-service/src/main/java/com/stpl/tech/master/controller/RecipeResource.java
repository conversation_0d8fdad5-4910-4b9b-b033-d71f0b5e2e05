/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.controller;


import com.stpl.tech.master.core.data.vo.ScmMissingPriceResponse;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.InvalidRequestException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.domain.model.CondimentGroupData;
import com.stpl.tech.master.domain.model.DispenserBulkRequest;
import com.stpl.tech.master.domain.model.DispenserRecipeRequest;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductRecipeMappingRequest;
import com.stpl.tech.master.monk.helper.MonkRecipeVersionHelper;
import com.stpl.tech.master.recipe.model.DispenserConfig;
import com.stpl.tech.master.recipe.model.DispenserRecipeDetail;
import com.stpl.tech.master.recipe.model.DispenserTagsMapping;
import com.stpl.tech.master.recipe.model.IngredientType;
import com.stpl.tech.master.recipe.model.RecipeCost;
import com.stpl.tech.master.recipe.model.RecipeCostData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.master.recipe.model.RecipeMediaDetail;
import com.stpl.tech.master.recipe.model.RecipeProfile;
import com.stpl.tech.master.recipe.model.RecipeUpdateLogDetail;
import com.stpl.tech.master.recipe.model.RecipeUpdateRequest;
import com.stpl.tech.master.recipe.model.UploadRecipeMediaResponse;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeDetail;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeVersionData;
import com.stpl.tech.master.recipe.monk.model.MonkRecipes;
import com.stpl.tech.master.recipe.monk.model.MonkRecipesVersionRequest;
import com.stpl.tech.master.service.model.RecipeUpdateCommandData;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.domain.RequestContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.RECIPE_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;


@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + RECIPE_SERVICES_ROOT_CONTEXT)
public class RecipeResource extends CommonRecipeResource {

    /**
     *
     */
    private static final int MONK_RECIPE_MODE = 8;
    private static final Logger LOG = LoggerFactory.getLogger(RecipeResource.class);

    @Autowired
    private RecipeCache recipeCache;

    @Autowired
    private MasterDataCache masterDataCache;

    @RequestMapping(method = RequestMethod.POST, value = "findAll", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<RecipeDetail> findAllRecipes() throws DataNotFoundException {
        LOG.info("Find All recipes for all product and dimesions");
        return getRecipeService().findAll();
        // Filter here
    }

    @RequestMapping(method = RequestMethod.POST, value = "find-all-with-exclude-ids", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<RecipeDetail> findAllRecipes(@RequestBody List<Integer> excludeIds) throws DataNotFoundException {
        LOG.info("Find All recipes for all product and dimesions with excludeIds {}", excludeIds);
        return getRecipeService().findAll(excludeIds);
    }

    @RequestMapping(method = RequestMethod.POST, value = "active-excluded-recipes", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<RecipeDetail> findAllActiveRecipes(@RequestBody List<Integer> excludeIds) throws DataNotFoundException {
        LOG.info("Find All Active recipes for all product and dimesions with excludeIds {}", excludeIds);
        return getRecipeService().findAllActive(excludeIds);
    }


    @RequestMapping(method = RequestMethod.POST, value = "monk/findAll", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<MonkRecipeDetail> findAllMonkRecipes() throws DataNotFoundException {
        LOG.info("Find All MONK recipes for all product and dimesions");
        return getRecipeService().findAllMonk();
    }

    @RequestMapping(method = RequestMethod.POST, value = "findContainigName", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<RecipeDetail> findRecipes(@RequestParam final String name) throws DataNotFoundException {
        LOG.info("Find  recipes for  product name like " + name);
        return getRecipeService().findRecipesContainingName(name);
    }

    @RequestMapping(method = RequestMethod.POST, value = "monk/findContainigName", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<MonkRecipeDetail> findMonkRecipes(@RequestParam final String name) throws DataNotFoundException {
        LOG.info("Find  MONK recipes for  product name like " + name);
        return getRecipeService().findMonkRecipesContainingName(name);
    }

    /**
     * Download all recipes
     *
     * @return View
     */
    @RequestMapping(method = RequestMethod.POST, value = "get-all-recipe-dump")
    public View getAllRecipeViewExcel() {
        List<RecipeDetail> details = getRecipeService().findAll();
        if (RequestContext.isContextAvailable()) {
            List<Integer> mappedBrands = MasterUtil.getMappedBrands();
            details = details.stream().filter(d -> {
                Product p = masterDataCache.getProduct(d.getProduct().getProductId());
                return (Objects.isNull(p) || mappedBrands.contains(p.getBrandId()));
            }).collect(Collectors.toList());
        }
        String fileName = "\"All Recipe Detail - " + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xls\"";
        return getRecipeHelper().getRecipeDetailView(details, fileName);

    }

    /**
     * Download all recipes
     *
     * @return View
     */
    @RequestMapping(method = RequestMethod.POST, value = "monk/get-all-recipe-dump")
    public View getAllMonkRecipeViewExcel() {
        List<MonkRecipeDetail> details = getRecipeService().findAllMonk();
        String fileName = "\"All Monk Recipe Detail - " + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xls\"";
        return getRecipeHelper().getMonkRecipeDetailView(8, details, fileName);

    }

    /**
     * Download all recipes with cost
     *
     * @return View
     */
    @RequestMapping(method = RequestMethod.POST, value = "get-all-recipe-with-cost-dump")
    public View getAllRecipeWithCostViewExcel() {
        List<RecipeDetail> details = getRecipeService().findAll();
        return getRecipeCostView(
                "\"All Recipe Detail With Cost- " + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xls\"", details);

    }

    /**
     * Download all recipes
     *
     * @return View
     */
    @RequestMapping(method = RequestMethod.POST, value = "get-all-recipe-cost-dump")
    public View getAllRecipeCostViewExcel() {
        List<RecipeDetail> details = getRecipeService().findAll();
        return getView("\"All Recipe Cost Detail - " + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xls\"",
                details);
    }

    /**
     * Method to add new recipe
     *
     * @param detail
     * @return
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "add", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public int addRecipe(@RequestBody final RecipeDetail detail) throws DataNotFoundException, InvalidRequestException {
        if (AppUtils.getBusinessDate(detail.getStartDate()).compareTo(AppUtils.getBusinessDate()) > 0) {
            LOG.info("Adding recipe for product {} and dimension {}", detail.getProduct().getProductId(), detail.getDimension().getInfoId());
            int recipeId = getRecipeService().addRecipeAfterCheck(detail);
            getRecipeHelper().notify(getRecipeHelper().getRecipeAddTemplate(detail));
            return recipeId;
        } else {
            throw new InvalidRequestException("Start date should be greater than current business date");
        }
    }


    @RequestMapping(method = RequestMethod.POST, value = "add-media",
            consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public int addRecipeMedia(@RequestBody final RecipeMediaDetail detail) throws DataNotFoundException {
        LOG.info("Request to add recipe media detail: {}", detail);
        return getRecipeService().addRecipeMedia(detail);
    }

    @RequestMapping(method = RequestMethod.POST, value = "bulk-upload-recipe-media", consumes = MediaType.MULTIPART_FORM_DATA, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<RecipeMediaDetail> bulkUploadRecipeMedia(HttpServletRequest request, @RequestParam(value = "file") final MultipartFile file) {
        LOG.info("Request to bulk upload recipe media");
        return getRecipeService().bulkUploadRecipeMedia(file);
    }

    @RequestMapping(method = RequestMethod.POST, value = "bulk-upload-recipe-condiment", consumes = MediaType.MULTIPART_FORM_DATA, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<RecipeDetail> bulkUploadRecipeCondiment(HttpServletRequest request, @RequestParam(value = "file") final MultipartFile file) {
        LOG.info("Request to bulk upload recipe media");
        return getRecipeService().bulkUploadRecipeCondiment(file);
    }

    @RequestMapping(method = RequestMethod.GET, value = "recipe-condiment-sheet-template")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getRecipeSheetTemplate() {
        LOG.info("Request to get Recipe Condiment sheet");
        return getRecipeService().getBulkUploadRecipeSheetTemplate();
    }

    @RequestMapping(method = RequestMethod.GET, value = "recipe-src-condiment")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String,List<CondimentGroupData> > getSrcToCondimentMapping(){
        LOG.info("Request to get Recipe To Source Condiment Map");
        return getRecipeService().getSrcToCondimentMapping();
    }



    @RequestMapping(method = RequestMethod.POST, value = "upload-recipe-media",
            produces = MediaType.APPLICATION_JSON, consumes = MediaType.MULTIPART_FORM_DATA)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UploadRecipeMediaResponse uploadRecipeMedia(HttpServletRequest request,
                                                       @RequestParam(value = "mediaType") String mediaType,
                                                       @RequestParam(value = "file") final MultipartFile file) {
        LOG.info("Request to upload recipe media");
        return getRecipeService().saveRecipeMedia(mediaType, file);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-recipe-steps-media", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<RecipeMediaDetail> getRecipeMediaStepDetailsByRecipeId(@RequestParam final int recipeId) {
        LOG.info("Request to get recipe step media for recipe id: {}", recipeId);
        return getRecipeService().findRecipeStepMediaDetailByRecipeId(recipeId);
    }

    /**
     * Method to add new recipe
     *
     * @param request
     * @return
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "add-clone", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public int addInProgressRecipe(@RequestBody final RecipeUpdateRequest request) throws DataNotFoundException, InvalidRequestException {
        RecipeDetail detail = request.getRecipeDetail();
        LOG.info("Adding recipe for product {} and dimension {}", detail.getProduct().getProductId(),
                detail.getDimension().getInfoId());
        if (AppUtils.getBusinessDate(detail.getStartDate()).compareTo(AppUtils.getBusinessDate()) > 0) {
            detail.setStatus("IN_PROGRESS");
            detail.setCreationDate(AppUtils.getCurrentTimestamp());
            int recipeId = getRecipeService().addRecipe(detail);

            request.getRecipeUpdateLogDetail().setRecipeId(recipeId);
            request.getRecipeUpdateLogDetail().setUpdateLog("Request to add new IN_PROGRESS recipe.");
            getRecipeService().updateRecipeLog(request.getRecipeUpdateLogDetail());

            getRecipeHelper().notify(getRecipeHelper().getRecipeAddTemplate(detail));
            return recipeId;
        } else {
            throw new InvalidRequestException("Start date should be greater than current business date");
        }
    }

    /**
     * Method to add new recipe
     *
     * @param detail
     * @return
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "monk/add", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public int addMonkRecipe(@RequestBody final MonkRecipeDetail detail) throws DataNotFoundException {
        LOG.info("Adding recipe for product {} and dimension {}", detail.getProduct().getProductId(),
                detail.getDimension().getInfoId());
        int recipeId = getRecipeService().addMonkRecipe(MONK_RECIPE_MODE, detail);
        getRecipeHelper().notify(getRecipeHelper().getMonkRecipeAddTemplate(detail));
        return recipeId;
    }

    /**
     * Method to update recipe
     *
     * @param request
     * @return
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public int updateRecipe(@RequestBody final RecipeUpdateRequest request) throws DataNotFoundException, InvalidRequestException {
        if (AppUtils.getBusinessDate(request.getRecipeDetail().getStartDate()).compareTo(AppUtils.getBusinessDate()) > 0) {
            LOG.info("Update recipe for product {} and dimension {}", request.getRecipeDetail().getProduct().getProductId(),
                    request.getRecipeDetail().getDimension().getInfoId());

            int recipeId = getRecipeService().updateRecipe(request.getRecipeDetail());


            request.getRecipeUpdateLogDetail().setRecipeId(recipeId);
            request.getRecipeUpdateLogDetail().setUpdateLog("Recipe edit requested.");
            getRecipeService().updateRecipeLog(request.getRecipeUpdateLogDetail());
            getRecipeHelper().notify(getRecipeHelper().getRecipeUpdateTemplate(request.getRecipeDetail()));
            return recipeId;
        } else {
            throw new InvalidRequestException("Start date should be greater than current business date");
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "approve-recipe", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public int approveRecipe(@RequestBody final RecipeUpdateRequest request, @RequestParam Boolean sentForApproval) throws InvalidRequestException {
        
        LOG.info(sentForApproval ? "Requested Approval" : "Approve" + " recipe for Recipe Id : {}", request.getRecipeDetail().getRecipeId());
        checkRecipeStatus(request, "APPROVAL", sentForApproval);
//        double diffInHours = AppUtils.getDateDifferenceInMinutes(AppUtils.getBusinessDate(request.getRecipeDetail().getStartDate()), AppUtils.getCurrentTimestamp());
//        if (sentForApproval && diffInHours <= 0 && diffInHours > -360) {
//            throw new InvalidRequestException("Can not Send Recipe For Approval . Recipe Should be sent for Approval before 6 Hours of the Start Date Of Recipe..!");
//        }
//        if (AppUtils.getDate((request.getRecipeDetail().getStartDate())).compareTo(AppUtils.getDate(AppUtils.getCurrentTimestamp())) == 0) {
//            throw new InvalidRequestException(sentForApproval ? "Can not Send Recipe For Approval . Recipe Should be sent for Approval before 6 Hours of the Start Date Of Recipe..!" :
//                    "Can not Approve Recipe. Recipe Should be Approved before the Start Date Of Recipe..!");
//        }
//        if (!sentForApproval) {
//            Integer approvedBy = request.getRecipeUpdateLogDetail().getUpdatedBy().getId();
//            if (request.getRecipeDetail().getApprovalRequestedBy().equals(approvedBy)) {
//                throw new InvalidRequestException("Person Who Requested For Approval Can not approve the request...!");
//            }
//        }
        int recipeId = getRecipeService().approveRecipe(request.getRecipeDetail(), request.getRecipeUpdateLogDetail().getUpdatedBy().getId(), sentForApproval);
        if (recipeId != -1) {
            request.getRecipeUpdateLogDetail().setRecipeId(recipeId);
            request.getRecipeUpdateLogDetail().setUpdateLog(sentForApproval ? "Request For Recipe Approval" : "Recipe Approve");
            getRecipeService().updateRecipeLog(request.getRecipeUpdateLogDetail());
            getRecipeHelper().notify(getRecipeHelper().getRecipeApproveTemplate(request.getRecipeDetail(), sentForApproval));
            if (!sentForApproval) {
                getRecipeService().markInProgressToActiveRecipes(request.getRecipeDetail().getRecipeId(), request.getRecipeUpdateLogDetail().getUpdatedBy().getId());
            }
            return recipeId;
        } else {
            throw new InvalidRequestException("Something Went Wrong....!");
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "reject-recipe", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public int rejectRecipe(@RequestBody final RecipeUpdateRequest request, @RequestParam Boolean sentForRejection) throws InvalidRequestException {
        LOG.info(sentForRejection ? "Reject" : "Cancelled Request For Approval" + " recipe for Recipe Id : {}", request.getRecipeDetail().getRecipeId());
        checkRecipeStatus(request, "REJECTION", sentForRejection);
//        if (sentForRejection) {
//            Integer rejectedBy = request.getRecipeUpdateLogDetail().getUpdatedBy().getId();
//            if (request.getRecipeDetail().getApprovalRequestedBy().equals(rejectedBy)) {
//                throw new InvalidRequestException("Person Who Requested For Approval Can not Reject the request...!");
//            }
//        }
        int recipeId = getRecipeService().rejectRecipe(request.getRecipeDetail(), request.getRecipeUpdateLogDetail().getUpdatedBy().getId(), sentForRejection);
        if (recipeId != -1) {
            request.getRecipeUpdateLogDetail().setRecipeId(recipeId);
            request.getRecipeUpdateLogDetail().setUpdateLog(sentForRejection ? "Recipe Rejected" : "Cancelled Request For Approval");
            getRecipeService().updateRecipeLog(request.getRecipeUpdateLogDetail());
            getRecipeHelper().notify(getRecipeHelper().getRecipeRejectTemplate(request.getRecipeDetail(), sentForRejection));
            if (AppUtils.getBusinessDate(request.getRecipeDetail().getStartDate()).compareTo(AppUtils.getBusinessDate()) <= 0) {
                getRecipeService().updateRecipeProfileStatus(request.getRecipeDetail().getRecipeId(),request.getRecipeUpdateLogDetail().getUpdatedBy().getId());
            }
            return recipeId;
        } else {
            throw new InvalidRequestException("Something Went Wrong....!");
        }
    }

    @Scheduled(cron = "0 0 4,5 * * *", zone = "GMT+05:30")
    @RequestMapping(method = RequestMethod.POST, value = "inactive-pending-approval-recipes", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void markRecipeInActive() {
        LOG.info("Marking Recipes As In Active");
        getRecipeService().markRecipeInActive();
    }

    @Scheduled(cron = "0 0 17 * * *", zone = "GMT+05:30")
    @RequestMapping(method = RequestMethod.POST, value = "notify-recipes-for-approval", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void notifyRecipesForApproval() {
        LOG.info("Running Cron to notify recipes for approval");
        getRecipeService().notifyRecipesForApproval(false, null);
    }

    @Scheduled(cron = "0 0 18 * * *", zone = "GMT+05:30")
    @RequestMapping(method = RequestMethod.POST, value = "mark-in-progress-recipe-to-active", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void markInProgressToActiveRecipes() throws InvalidRequestException {
        LOG.info("Running Cron to Change Status From In Progress To Active");
        getRecipeService().markInProgressToActiveRecipes(null, null);
    }

    private void checkRecipeStatus(RecipeUpdateRequest request, String action, Boolean isInProgress) throws InvalidRequestException {
        List<RecipeDetail> recipeDetails = getRecipeService().findByRecipeId(request.getRecipeDetail().getRecipeId());
        if (Objects.nonNull(recipeDetails) && !recipeDetails.isEmpty()) {
            if (Objects.nonNull(recipeDetails.get(0).getStatus())) {
                if (action.equalsIgnoreCase("APPROVAL")) {
                    if (isInProgress) {
                        if(!recipeDetails.get(0).getStatus().equalsIgnoreCase("IN_PROGRESS")) {
                            throw new InvalidRequestException("Action NOT ALLOWED.Recipe is in " + recipeDetails.get(0).getStatus() +" .Please Refresh and Try Again ..!");
                        }
                    } else {
                        if(!recipeDetails.get(0).getStatus().equalsIgnoreCase("PENDING_APPROVAL")) {
                            throw new InvalidRequestException("Action NOT ALLOWED.Recipe is in " + recipeDetails.get(0).getStatus() +" .Please Refresh and Try Again ..!");
                        }
                    }
                } else {
                    if (!recipeDetails.get(0).getStatus().equalsIgnoreCase("PENDING_APPROVAL")) {
                        throw new InvalidRequestException("Action NOT ALLOWED.Recipe is in " + recipeDetails.get(0).getStatus() + " .Please Refresh and Try Again ..!");
                    }
                }
            }
        }
    }

    /**
     * Method to update recipe
     *
     * @param recipeId
     * @return
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "get-logs", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<RecipeUpdateLogDetail> updateRecipe(@RequestBody final Integer recipeId) throws DataNotFoundException {
        LOG.info("Request to get log for recipe id: {}", recipeId);
        return getRecipeService().getUpdateLogs(recipeId);
    }

    /**
     * Method to update recipe
     *
     * @param detail
     * @return
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "monk/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public int updateMonkRecipe(@RequestBody final MonkRecipeDetail detail) throws DataNotFoundException {
        LOG.info("Update recipe for product {} and dimension {}", detail.getProduct().getProductId(),
                detail.getDimension().getInfoId());
        int recipeId = getRecipeService().updateMonkRecipe(MONK_RECIPE_MODE, detail);
        getRecipeHelper().notify(getRecipeHelper().getMonkRecipeUpdateTemplate(detail));
        return recipeId;
    }

    /**
     * Types of Ingredients available
     *
     * @return IngredientType
     */
    @RequestMapping(method = RequestMethod.GET, value = "ingredient-type", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public IngredientType[] getIngredientType() {
        return IngredientType.values();
    }

    /**
     * Search recipe
     *
     * @param productId
     * @param dimensionId
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "find-recipe", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<RecipeDetail> findRecipe(@RequestParam(name = "productId") int productId,
                                         @RequestParam(name = "dimensionId") int dimensionId,
                                         @RequestParam(required = false) boolean active) {
        List<RecipeDetail> list = getRecipeService().findByProductIdAndDimension(productId, dimensionId);
        if (active) {
            list = list.stream().filter(recipeDetail -> !recipeDetail.getStatus().equalsIgnoreCase(AppConstants.IN_ACTIVE))
                    .map(e -> {
                        if (Objects.nonNull(e.getApprovedBy())) {
                            e.setApprovedByName(AppUtils.getCreatedBy(getMasterDataCache().getEmployee(e.getApprovedBy()), e.getApprovedBy()));
                        }
                        return e;
                    })
                    .collect(Collectors.toList());
        }
        return list;
    }

    /**
     * Search recipe
     *
     * @param productId
     * @param dimensionId
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "monk/find-recipe", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<MonkRecipeDetail> findMonkRecipe(@RequestParam(name = "productId") int productId,
                                                 @RequestParam(name = "dimensionId") int dimensionId) {
        return getRecipeService().findMonkRecipeByProductIdAndDimension(productId, dimensionId);
    }

    /**
     * Search recipe
     *
     * @param productId
     * @param uom
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "find-recipe-by-uom", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<RecipeDetail> findRecipeByUom(@RequestParam(name = "productId") int productId,
                                              @RequestParam(name = "uom") String uom) {
        List<RecipeDetail> list = getRecipeService().findByProductIdAndUom(productId, uom);
        return list;
    }

    @RequestMapping(method = RequestMethod.GET, value = "find-recipe-containing-productId", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<RecipeDetail> findRecipe(@RequestParam(name = "productId") int productId) {
        List<RecipeDetail> list = getRecipeService().findSCMRecipeContainigProductId(productId);
        return list;
    }

    @RequestMapping(method = RequestMethod.GET, value = "find-recipe-by-recipe-id", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<RecipeDetail> findRecipeByRecipeId(@RequestParam(name = "recipeId") int recipeId) {
        return getRecipeService().findRecipeByRecipeId(recipeId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "re-activate-recipe", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean reActivateRecipe(@RequestParam(name = "recipeId") Integer recipeId,
                                    @RequestParam(name = "productId") Integer productId,
                                    @RequestParam(name = "dimensionInfoId") Integer dimensionInfoId,
                                    @RequestParam(name = "dimension") String dimension,
                                    @RequestParam String profile,
                                    @RequestParam Integer userId) throws InvalidRequestException {
        return getRecipeService().reActivateRecipe(recipeId, productId, dimensionInfoId, dimension, userId, profile);
    }

    /**
     * Search recipe using name
     *
     * @param name
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "find-recipe/name", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<RecipeDetail> findRecipe(@RequestParam(name = "recipeName") String name) {
        return getRecipeService().findByName(name);
    }

    /**
     * Remove recipe
     *
     * @param detail
     * @return
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "remove", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<IdCodeName> removeRecipe(@RequestBody final RecipeDetail detail) throws DataNotFoundException {
        LOG.info("Remove recipe for product {} and dimension {}", detail.getProduct().getProductId(),
                detail.getDimension().getInfoId());
        List<IdCodeName> result = getRecipeService().removeRecipe(detail);
        if (result != null && result.size() == 0) {
            getRecipeHelper().notify(getRecipeHelper().getRecipeRemoveTemplate(detail));
        }
        return result;
    }

    /**
     * Remove recipe
     *
     * @param detail
     * @return
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "monk/remove", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean removeRecipe(@RequestBody final MonkRecipeDetail detail) throws DataNotFoundException {
        LOG.info("Remove recipe for product {} and dimension {}", detail.getProduct().getProductId(),
                detail.getDimension().getInfoId());
        boolean result = getRecipeService().removeMonkRecipe(detail);
        getRecipeHelper().notify(getRecipeHelper().getMonkRecipeRemoveTemplate(detail));
        return result;
    }

    @RequestMapping(method = RequestMethod.POST, value = "cost/update", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean updateRecipeCost(@RequestBody List<RecipeCostData> data) throws DataNotFoundException {
        LOG.info("Request To update cost of recipes");
        getRecipeService().updateRecipeCost(data);
        return true;
    }

    /**
     * Request to save recipes again, run i case we need to recalculate anything
     * for all recipes
     *
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "save-all", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void saveAllRecipes() throws DataNotFoundException {
        LOG.info("Re-save All recipes for all product and dimesions");
        List<RecipeDetail> recipes = getRecipeService().findAll();
        int counter = recipes.size();
        LOG.info("total recipes: {}", counter);
        getRecipeService().updateAllRecipes(recipes);
    }

    /**
     * Download a particular recipe
     *
     * @param recipeId
     * @return view
     */
    @RequestMapping(method = RequestMethod.POST, value = "get-recipe-dump")
    public View getRecipeViewExcel(@RequestBody int recipeId) {
        List<RecipeDetail> details = getRecipeService().findByRecipeId(recipeId);
        List<RecipeCost> costs = new ArrayList<>();
        for (RecipeDetail detail : details) {
            if (detail.getProduct().getType() == AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE) {
                continue;
            }
            costs.add(calculateRecipeCost(detail));
        }
        String fileName = "\"Recipe Detail - " + details.get(0).getName() + " - "
                + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xls\"";

        return getRecipeHelper().getRecipeView(costs, details, fileName);
    }

    @RequestMapping(method = RequestMethod.POST, value = "monk/create-new-version", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public MonkRecipeVersionData createNewVersion(@RequestBody final RecipeUpdateCommandData command) {
        List<MonkRecipeVersionData> oldVersions = getRecipeService().findVersionDataByStatus("CREATED");
        List<MonkRecipeVersionData> activeVersions = getRecipeService().findVersionDataByStatus("ACTIVATED");
        MonkRecipeVersionData current = new MonkRecipeVersionData();
        current.setGenetrationTime(AppUtils.getCurrentTimestamp());
        current.setUpdatedById(command.getEmployeeId());
        current.setUpdatedByName(command.getEmployeeName());
        current.setStatus("CREATED");
        MonkRecipeVersionHelper.process(current,
                activeVersions != null && activeVersions.size() > 0 ? activeVersions.get(0) : null,
                getRecipeService().findAllMonk());
        if (oldVersions != null) {
            for (MonkRecipeVersionData old : oldVersions) {
                old.setStatus("CANCELLED");
                getRecipeService().updateMonkRecipeVersion(old);
            }
        }
        return getRecipeService().addMonkRecipeVersion(current);
    }

    @RequestMapping(method = RequestMethod.POST, value = "monk/activate-current-version", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void activateNewVersion(@RequestBody final RecipeUpdateCommandData command) {
        List<MonkRecipeVersionData> created = getRecipeService().findVersionDataByStatus("CREATED");
        List<MonkRecipeVersionData> activeVersions = getRecipeService().findVersionDataByStatus("ACTIVATED");
        if (created != null) {
            for (MonkRecipeVersionData old : created) {
                old.setStatus("ACTIVATED");
                old.setActivatedById(command.getEmployeeId());
                old.setActivatedByName(command.getEmployeeName());
                old.setActivationTime(AppUtils.getCurrentTimestamp());
                getRecipeService().updateMonkRecipeVersion(old);
            }
        }
        if (activeVersions != null) {
            for (MonkRecipeVersionData old : activeVersions) {
                old.setStatus("CLOSED");
                old.setClosedById(command.getEmployeeId());
                old.setClosedByName(command.getEmployeeName());
                old.setClosureTime(AppUtils.getCurrentTimestamp());
                getRecipeService().updateMonkRecipeVersion(old);
            }
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "monk/find-version-by-status", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public MonkRecipeVersionData findVersion(@RequestParam("status") final String status) {
        List<MonkRecipeVersionData> created = getRecipeService().findVersionDataByStatus(status);
        if (created != null && created.size() > 0) {
            return created.get(0);
        }
        return null;
    }

    @RequestMapping(method = RequestMethod.GET, value = "recipe/profiles", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<RecipeProfile> getRecipeProfiles(@RequestParam("type") final String type) {
        return getRecipeService().getRecipeProfiles(type);
    }

    @RequestMapping(method = RequestMethod.GET, value = "recipe-profile-product-dimension", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<String> RecipeProfilesOfProduct(@RequestParam(name = "productId") int productId,
                                                @RequestParam(name = "dimensionId") int dimensionId) {
        List<String> list = getRecipeService().findProfilesByProductIdAndDimension(productId, dimensionId);
        return list;
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @PostMapping("recipe-profile-products-dimensions")
    public Map<String, Set<String>> recipeProfilesForProductsAndDimensions(@RequestBody ProductRecipeMappingRequest request) {
        return getRecipeService().findRecipeByProductsAndDimensions(request);
    }

    @RequestMapping(method = RequestMethod.GET, value = "recipe-profile-product", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String, List<String>> RecipeProfilesOfProduct(@RequestParam(name = "productId") int productId) {
        return getRecipeService().findProfilesByProductId(productId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "monk/recipe-profile", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public MonkRecipes checkAndSendMonkRecipes(@RequestParam(name = "u", required = false) Integer unitId,
                                               @RequestParam(name = "v", required = false) String version) {
        Pair<String,String> unitMonkRecipeProfileVersion = null;
        if (Objects.nonNull(unitId)) {
            unitMonkRecipeProfileVersion = recipeCache.getMonkRecipeProfileVersionOfUnit(unitId);
        }
        return getRecipeService().checkAndSendMonkRecipes(unitId, version, unitMonkRecipeProfileVersion);
    }

    @RequestMapping(method = RequestMethod.GET, value = "monk/recipe-profile/force", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public MonkRecipes getMonkRecipes(@RequestParam(name = "u", required = false) Integer unitId) {
        return getRecipeService().getMonkRecipes(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "monk/recipe-profile/version-update-status", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean saveMonkRecipeVersionStatus(@RequestParam(name = "u") Integer unitId,
                                               @RequestParam(name = "v") String version,
                                               @RequestParam(name = "c") Integer count,
                                               @RequestParam(name = "e") Integer employeeId) {
        LOG.info("Request to save updated version on monk recipe ::: unit id : {} version : {}",unitId, version);
        return getRecipeService().saveUpdatedVersionDetail(unitId, version, count, employeeId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "save-dispenser-data", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public DispenserRecipeDetail saveDispenserData(@RequestBody DispenserRecipeRequest dispenserRecipeRequest) {
        return getRecipeService().saveDispenserData(dispenserRecipeRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-dispenser-data", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<DispenserRecipeDetail> getDispenserData() {
        return getRecipeService().getDispenserData();
    }

    @RequestMapping(method = RequestMethod.POST, value = "save-dispense-tag", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public Boolean saveDispenseTag(@RequestBody List<String> tags) {
        return getRecipeService().saveDispenseTag(tags);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-dispense-tag", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public DispenserConfig getDispenseTag() {
        return getRecipeService().getDispenseTag();
    }

    @RequestMapping(method = RequestMethod.GET, value = "recipes-with-dispensed", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<HashMap<String, HashMap<String, DispenserTagsMapping>>> getRecipesWithDispensed() {

        return getRecipeService().getRecipesWithDispensed();
    }

    @RequestMapping(method = RequestMethod.POST, value = "label-bulk-recipes-with-dispenseTags", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<RecipeDetail> setRecipesWithDispensedTag(@RequestBody DispenserBulkRequest dispenserBulkRequest) {
        LOG.info("request to set tag for product {}", dispenserBulkRequest.getProducts());
        return getRecipeService().setRecipesWithDispensedTag(dispenserBulkRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = "monk/version/save", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<MonkRecipeVersionData> saveMonkVersion(@RequestBody final List<MonkRecipesVersionRequest> requests) throws DataNotFoundException {
        LOG.info("request to save monk recipe version ");
      return  getRecipeService().saveMonkRecipeVersions(requests);
    }

    @RequestMapping(method = RequestMethod.GET, value = "monk/profile/sheet")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getProfileSheet(@RequestParam final String profile) throws DataNotFoundException {
        LOG.info("request to fetch sheet for profile {} ",profile);
        return  getRecipeService().getSheetForRecipes(profile);
    }

    @RequestMapping(method = RequestMethod.GET, value = "monk/profile/basic-detail", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public MonkRecipeVersionData getBasicDetailForProfile(@RequestParam final String profile) throws DataNotFoundException {
        LOG.info("request to fetch basic detail for profile {} ",profile);
        return  getRecipeService().getBasicDetailForProfile(profile);
    }
    @RequestMapping(method = RequestMethod.GET, value = "menu-to-scm", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void menuToSCMProductMap() {
        LOG.info("request to run menu to SCM product map job ");
        getRecipeService().executeMenuToSCMProductMap();
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-recipe-profile-status",consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean updateRecipeProfileStatus(@RequestParam Integer recipeId,@RequestParam Integer userId) {
        LOG.info("request to Update Recipe Profile Status");
        return  getRecipeService().updateRecipeProfileStatus(recipeId,userId);
    }



    @RequestMapping(method = RequestMethod.POST, value = "get-approved-recipes-by-today",consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ScmMissingPriceResponse getApprovedRecipesByToday(@RequestBody Map<Integer,Integer> map){
          return getRecipeService().getApprovedRecipesByToday();
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-remove-products-to-paid-addons-of-recipes",consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addRemoveProductsToPaidAddonsOfRecipes(@RequestBody Map<Integer, List<Integer>> recipesForUpdate, @RequestParam boolean needToRemove) throws DataNotFoundException {
        return getRecipeService().addRemoveProductsToPaidAddonsOfRecipes(recipesForUpdate, needToRemove);
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @PostMapping("update-recipe")
    public ResponseEntity<Map<String, List<String>>> updateRecipeByProducts(@RequestBody MultipartFile excelFile) throws IOException {
        return getRecipeService().updateRecipeByProducts(excelFile);
    }


}
