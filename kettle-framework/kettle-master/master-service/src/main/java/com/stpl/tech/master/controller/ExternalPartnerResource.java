package com.stpl.tech.master.controller;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.EXTERNAL_PARTNER_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

import javax.ws.rs.core.MediaType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.external.partner.service.ExternalAPIService;

/**
 * Created by <PERSON><PERSON>
 */
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + EXTERNAL_PARTNER_ROOT_CONTEXT) // 'external-partner'
public class ExternalPartnerResource {

	private static final Logger LOG = LoggerFactory.getLogger(ExternalPartnerResource.class);

	@Autowired
	private ExternalAPIService externalAPIService;

	@Autowired
	private MasterProperties props;

	@RequestMapping(method = RequestMethod.GET, value = "add/partner", produces = MediaType.APPLICATION_JSON)
	public void addPartner(@RequestParam("partnerName") String partnerName) {
		LOG.info("Adding partner with name {}", partnerName);
		externalAPIService.addPartner(partnerName, props.getEnvironmentType().name());
	}

}
