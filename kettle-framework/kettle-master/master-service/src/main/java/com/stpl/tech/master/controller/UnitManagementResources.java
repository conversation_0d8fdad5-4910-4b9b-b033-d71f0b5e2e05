
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.master.controller;

import com.hazelcast.multimap.MultiMap;
import com.stpl.tech.kettle.report.metadata.model.TrueCallerSettings;
import com.stpl.tech.master.UnitStatusData;
import com.stpl.tech.master.core.CacheReferenceType;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.UnitClosureStateEnum;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.MasterException;
import com.stpl.tech.master.core.external.activity.service.ActivityLoggerService;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.MasterDataCacheService;
import com.stpl.tech.master.core.service.MasterMetadataService;
import com.stpl.tech.master.core.service.UnitClosureService;
import com.stpl.tech.master.core.service.UnitManagementService;
import com.stpl.tech.master.core.service.model.UnitClosureFormDataDomain;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.model.ApplicationVersionDetail;
import com.stpl.tech.master.data.model.ApplicationVersionDetailData;
import com.stpl.tech.master.data.model.ApplicationVersionEvent;
import com.stpl.tech.master.data.model.CacheReferenceMetadata;
import com.stpl.tech.master.data.model.FeedbackQuestionsDetail;
import com.stpl.tech.master.data.model.FeedbackQuestionsUnitMapping;
import com.stpl.tech.master.data.model.UnitIpAddressData;
import com.stpl.tech.master.data.model.UnitToPartnerDqrMapping;
import com.stpl.tech.master.data.model.UnitToPartnerEdcMapping;
import com.stpl.tech.master.data.mongo.AuditChangeLogTypes;
import com.stpl.tech.master.domain.model.ApkUploadResponse;
import com.stpl.tech.master.domain.model.ApplicationVersionDomian;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.BrandMapping;
import com.stpl.tech.master.domain.model.BusinessHourEvent;
import com.stpl.tech.master.domain.model.BusinessHourObject;
import com.stpl.tech.master.domain.model.CafeTimingChangeRequest;
import com.stpl.tech.master.domain.model.CancellationReason;
import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.CafeType;
import com.stpl.tech.master.domain.model.Department;
import com.stpl.tech.master.domain.model.Designation;
import com.stpl.tech.master.domain.model.Division;
import com.stpl.tech.master.domain.model.FeedbackQuesMappingDomain;
import com.stpl.tech.master.domain.model.FeedbackQuestionDomain;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdNameCategory;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.MenuType;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.TaxProfile;
import com.stpl.tech.master.domain.model.TransactionMetadata;
import com.stpl.tech.master.domain.model.TrimmedProductData;
import com.stpl.tech.master.domain.model.TrimmedProductVO;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitClosureEventDomain;
import com.stpl.tech.master.domain.model.UnitClosureFormMetaDataDomain;
import com.stpl.tech.master.domain.model.UnitClosureStateEventDomain;
import com.stpl.tech.master.domain.model.UnitContactDetails;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.master.domain.model.UnitIpAddressRequest;
import com.stpl.tech.master.domain.model.UnitMenuTypePrice;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.domain.model.UnitSubCategory;
import com.stpl.tech.master.domain.model.UnitToPartnerEdcMappingDetail;
import com.stpl.tech.master.domain.model.UnitVersionDetail;
import com.stpl.tech.master.domain.model.UnitVersionDomain;
import com.stpl.tech.master.domain.model.UnitWsToStationCategoryMappingRequest;
import com.stpl.tech.master.domain.model.VersionEventDomain;
import com.stpl.tech.master.locality.model.LocalityMapping;
import com.stpl.tech.master.monk.configuration.model.MonkAttr;
import com.stpl.tech.master.monk.configuration.model.MonkConfiguration;
import com.stpl.tech.master.monk.configuration.model.MonkConfigurationValue;
import com.stpl.tech.master.notification.UnitActivationEmail;
import com.stpl.tech.master.notification.UnitActivationReceipt;
import com.stpl.tech.master.readonly.domain.model.ProductPriceVO;
import com.stpl.tech.master.readonly.domain.model.ProductVO;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import com.stpl.tech.master.readonly.domain.model.UnitProductData;
import com.stpl.tech.master.readonly.domain.model.UnitProductRecipesKeys;
import com.stpl.tech.master.tax.model.CategoryTax;
import com.stpl.tech.master.tax.model.TaxCategory;
import com.stpl.tech.master.tax.model.TaxData;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.domain.RequestContext;
import com.stpl.tech.util.excelparser.ExcelWriter;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;
import static com.stpl.tech.master.service.core.MasterServiceConstants.UNIT_METADATA_ROOT_CONTEXT;


/**
 * Root resource (exposed at "unit-metadata" path)
 */

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + UNIT_METADATA_ROOT_CONTEXT) //    v1/unit-metadata
public class UnitManagementResources extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(UnitManagementResources.class);

    @Autowired
    private MasterMetadataService masterService;

    @Autowired
    private ActivityLoggerService loggerService;

    @Autowired
    private UnitManagementService unitService;

    @Autowired
    private MasterProperties props;

    @Autowired
    private MasterDataCacheService masterCacheImpl;

    @Autowired
    private MasterMetadataService masterMetadataService;

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    private TaxDataCache taxCache;

    @Autowired
    private Environment env;
    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private UnitClosureService unitClosureService;

    @RequestMapping(method = RequestMethod.GET, value = "countries", produces = MediaType.APPLICATION_JSON)
    public Collection<IdCodeName> allCountries() {
        LOG.info("Getting All countries");
        return masterCache.getAllCountries();
    }

    @RequestMapping(method = RequestMethod.GET, value = "states", produces = MediaType.APPLICATION_JSON)
    public Collection<IdCodeName> allStates(@RequestParam("countryId") int id) {
        LOG.info("Getting All states for the country {}", id);
        return masterCache.getAllStates(id);
    }

    @RequestMapping(method = RequestMethod.GET, value = "locations", produces = MediaType.APPLICATION_JSON)
    public Collection<Location> allLocations(@RequestParam("stateId") int id) {
        LOG.info("Getting All locations for the state {}", id);
        return masterCache.getAllLocations(id);
    }

    @RequestMapping(method = RequestMethod.GET, value = "cities", produces = MediaType.APPLICATION_JSON)
    public Set<String> cities() {
        LOG.info("Getting All locations for the state ");
        Set<String> cityNames = new HashSet<String>();
        MultiMap<Integer, Location> map= masterCache.getStateToLocation();
        Set<Integer> keys = map.keySet();
        for (Integer keyprint : keys) {
            Collection<Location> values = map.get(keyprint);
            for(Location value : values){
                cityNames.add(value.getCode());
            }
        }
        return cityNames;
    }

    @RequestMapping(method = RequestMethod.GET, value = "location-by-zone", produces = MediaType.APPLICATION_JSON)
    public Map<String, Set<Pair<Integer, String>>> getLocationByZone() {
        return unitService.getLocationByZone();
    }

    @RequestMapping(method = RequestMethod.GET, value = "cancellation-reasons", produces = MediaType.APPLICATION_JSON)
    public Collection<CancellationReason> allCancellationReason(@RequestParam("stateId") String source) {
        LOG.info("Getting All Cancellation Reasons {}", source);
        return masterCache.getCancellationReason(UnitCategory.valueOf(source));
    }

    @RequestMapping(method = RequestMethod.GET, value = "units", produces = MediaType.APPLICATION_JSON)
    public List<Unit> allUnits(@RequestParam("category") final String category)
        throws DataNotFoundException {
        LOG.info("Getting All units for a category {}", category);
        return masterService.getAllUnits(UnitCategory.valueOf(category));
    }

    @RequestMapping(method = RequestMethod.GET, value = "all-units", produces = MediaType.APPLICATION_JSON)
    public List<UnitBasicDetail> getAllUnits(@RequestParam("category") final String category,
                                             @RequestParam(name = "region", required = false) String region,
                                             @RequestParam(value = "testingUnit", required = false) Boolean excludeTestingUnit) {
        LOG.info("Getting All units for a category {} and region {} and testingUnit {}", category, region, excludeTestingUnit);
        return getUnitsByRegion(category, region, excludeTestingUnit);
    }

    @RequestMapping(method = RequestMethod.GET, value = "all-active-units", produces = MediaType.APPLICATION_JSON)
    public List<IdNameCategory> getAllActiveUnits(@RequestParam("category") final String category) {
        LOG.info("Getting All units for a category {}", category);
        List<UnitBasicDetail> units = null;
        units = getUnits(category, null);
        List<IdNameCategory> all = new ArrayList<>();
        if (units != null) {
            for (UnitBasicDetail detail : units) {
                if (detail.getStatus().equals(UnitStatus.ACTIVE)) {
                    IdNameCategory data = new IdNameCategory();
                    BeanUtils.copyProperties(detail, data);
                    data.setUnitZone(Objects.nonNull(detail.getUnitZone())
                            ? detail.getUnitZone() : null);
                    all.add(data);
                }
            }
        }
        return all;
    }

    @RequestMapping(method = RequestMethod.GET, value = "localities", produces = MediaType.APPLICATION_JSON)
    public List<LocalityMapping> getLocalities() {
        LOG.info("Getting All localities for COD");
        List<LocalityMapping> localityMappings = new ArrayList<>(masterCacheImpl.getAllLocalities().values());
        for (LocalityMapping ele : localityMappings) {
            if (Objects.nonNull(ele.getPrimaryUnitId())
                    && StringUtils.isNumeric(ele.getPrimaryUnitId())
                    && Objects.nonNull(masterCacheImpl.getUnit(Integer.valueOf(ele.getPrimaryUnitId())))) {
                ele.setZone(masterCacheImpl.getUnit(Integer.valueOf(ele.getPrimaryUnitId())).getUnitZone());
            }
        }
        return localityMappings;
    }

    @RequestMapping(method = RequestMethod.GET, value = "companies", produces = MediaType.APPLICATION_JSON)
    public List<Company> getCompanies() {
        LOG.info("Getting All companies");
        Integer companyId = RequestContext.getCompanyId();
        List<Company> companies = masterCache.getAllCompanies();
        if (Objects.nonNull(companyId)) {
            companies = companies.stream().filter(c -> c.getId() == companyId).collect(Collectors.toList());
        }
        return companies;
    }

    @RequestMapping(method = RequestMethod.GET, value = "all-units-list", produces = MediaType.APPLICATION_JSON)
    public Set<UnitBasicDetail> getAllUnits() {
        LOG.info("Getting All units ");
        Set<UnitBasicDetail> sortedUnits = new TreeSet<>(Comparator.comparing(UnitBasicDetail::getName));
        for (UnitBasicDetail unitBasicDetail : masterCache.getAllUnits()) {
            if (unitBasicDetail.getStatus().value().equals(AppConstants.ACTIVE)
                && !unitBasicDetail.getCategory().value().equals(UnitCategory.COD.value())) {
                sortedUnits.add(unitBasicDetail);
            }
        }
        return sortedUnits;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-data", produces = MediaType.APPLICATION_JSON)
    public Unit getUnit(@RequestBody final int unitId) throws DataNotFoundException {
        LOG.info("Getting Unit Details with ID {}", unitId);
        return masterService.getUnit(unitId, true);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-truecaller-setting", produces = MediaType.APPLICATION_JSON)
    public Unit getUnit(@RequestBody final Map<String, String> request, HttpServletRequest servletRequest)
        throws DataNotFoundException, DataUpdationException {
        Integer unitId = Integer.parseInt(request.get("unitId"));
        TrueCallerSettings settings = TrueCallerSettings.valueOf(request.get("setting"));
        LOG.info("Updating True Caller Setting for unitId {} with setting {}", unitId, settings);
        Unit unit = masterService.getUnit(unitId, true);
        unit.setTrueCallerEnabled(settings);
        Unit finalUnit = unitService.addUnit(unit);
        loggerService.addActivity(unit, finalUnit, getLoggedInUser(servletRequest));
        return finalUnit;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-product-data", produces = MediaType.APPLICATION_JSON)
    public Collection<Product> getUnitProductData(@RequestBody final int unitId)
        throws DataNotFoundException {
        LOG.info("Getting Unit Product Details with ID {}", unitId);
        return masterService.getUnitProducts(unitId, true);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-product-data-all", produces = MediaType.APPLICATION_JSON)
    public Collection<Product> getUnitProductDataAll(@RequestBody final int unitId)
        throws DataNotFoundException {
        LOG.info("Getting Unit Product Details with ID {}", unitId);
        return masterService.getAllUnitProducts(unitId, true, true);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-trimmed-product-data-all", produces = MediaType.APPLICATION_JSON)
    public Collection<TrimmedProductVO> getUnitTrimmedProductDataAll(@RequestBody final int unitId)
        throws DataNotFoundException {
        LOG.info("Getting Unit Trimmed Product Details with ID {}", unitId);
        return masterService.getAllUnitProductsPrices(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-trimmed-product-data-all-v1", produces = MediaType.APPLICATION_JSON)
    public Collection<TrimmedProductVO> getUnitTrimmedProductDataAllV1(@RequestBody final int unitId)
            throws DataNotFoundException {
        LOG.info("Getting Unit Trimmed Product Details with ID {}", unitId);
        return masterService.getAllUnitProductsPricesV1(unitId);
    }


    @RequestMapping(method = RequestMethod.POST, value = "unit-product-data/trim", produces = MediaType.APPLICATION_JSON)
    public Collection<IdCodeName> getUnitProductsTrimmed(@RequestBody final int unitId, @RequestParam Boolean all)
        throws DataNotFoundException {
        LOG.info("Getting Unit Product Details with ID {}", unitId);
        if (all == null) {
            all = true;
        }
        return masterService.getUnitProductsTrimmed(unitId, all);

    }

    @RequestMapping(method = RequestMethod.GET, value = "unit-product/trim", produces = MediaType.APPLICATION_JSON)
    public List<TrimmedProductData> getUnitProductsTrimmed()
            throws DataNotFoundException {
        LOG.info("Getting Unit Product and dimension" );
        return masterService.getUnitProductDimensions();
    }

    @RequestMapping(method = RequestMethod.GET, value = "takeaway-units", produces = MediaType.APPLICATION_JSON)
    public List<UnitBasicDetail> allTakeawayUnits() {
        LOG.info("Getting All units for takeaway");
        List<UnitBasicDetail> units = null;
        units = masterCache.getAllUnits().stream().filter(unit -> {
            boolean isTakeaway = false;
            isTakeaway = (unit.getNoOfTakeawayTerminals() > 0 || UnitCategory.TAKE_AWAY.equals(unit.getCategory()));
            return isTakeaway;
        }).collect(Collectors.toList());
        return units;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/add", produces = MediaType.APPLICATION_JSON)
    public int addUnit(@RequestBody final Unit unit, HttpServletRequest request)
        throws DataUpdationException, DataNotFoundException {
        LOG.info("Adding unit with name {} and cloning it from {}", unit.getName(), unit.getCloneUnitId());
        Unit data = unitService.addUnit(unit);
        Unit mongoUnit = data;
        masterService.saveAuditLog(unit.getId(), AuditChangeLogTypes.UNIT_DETAIL.value(), getLoggedInUser(request), mongoUnit,
                AuditChangeLogTypes.NEW_ENTRY.value());
        loggerService.addActivity(null, data, getLoggedInUser(request));
        ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext
                .getBean("taskExecutor");
        executor.execute(() -> {
            try {
                BusinessHourObject businessHourObject = new BusinessHourObject();
                businessHourObject.setEvent(BusinessHourEvent.NEW_UNIT_ADDED.value());
                businessHourObject.setOutletId(data.getId());
                businessHourObject.setOldBusinessHours(null);
                WebServiceHelper.postRequestWithAuthInternalWithTimeout(env.getProperty("zomoto.business.hours.url"),businessHourObject,boolean.class,env.getProperty("cp.client.token"),10,10);
            } catch (IOException e) {
                LOG.info("Fail to call Zomato For Updating Business Hour with Error  : {}",e);
            }
        });
        int unitId = data.getId();
        addUnitBrandMappings(request, unitId, unit.getBrands());
        return unitId;

    }

	@RequestMapping(method = RequestMethod.POST, value = "unit/refresh-cache", produces = MediaType.APPLICATION_JSON)
	public boolean refreshCache(@RequestParam final Integer unitId, @RequestParam final Boolean flushInventory)
			throws DataUpdationException, DataNotFoundException {
		LOG.info("Refresh cache for unit Id {}", unitId);
		return masterCacheImpl.refreshUnit(unitId, flushInventory);

	}

    @RequestMapping(method = RequestMethod.POST, value = "unit/update", produces = MediaType.APPLICATION_JSON)
    public int updateUnit(@RequestBody final Unit unit, HttpServletRequest request)
            throws DataUpdationException, DataNotFoundException, IllegalAccessException, EmailGenerationException {
        LOG.info("Updating unit with name {}", unit.getName());
        Unit oldUnit = masterService.getUnit(unit.getId(), false);
        Unit newUnit = unitService.updateUnit(unit);
        boolean isBusinessHourChanges = false;
        try{
            LOG.info("Trying To Send Email Of Changes Done On Unit : {}",unit.getName());
            List<String> toEmails = new ArrayList<>(Arrays.asList("<EMAIL>"));
            Unit mongoUnit = newUnit;
            Unit mongoUnitOld = oldUnit;
//            mongoUnitOld.setOperationalHours(null);
            masterService.saveAuditLog(unit.getId(), AuditChangeLogTypes.UNIT_DETAIL.value(), getLoggedInUser(request), mongoUnit,
                    AuditChangeLogTypes.UPDATE_ENTRY.value());

            loggerService.sendUnitChangeMail(mongoUnitOld, newUnit, masterCache.getEmployee(getLoggedInUser(request)), "UNIT", oldUnit.getId(),
                    toEmails, "Unit : " + oldUnit.getName());
        }catch (Exception e){
            LOG.info("Error While Sending Email Of Changes Done On Unit : {}",unit.getName(),e);
        }
        loggerService.addActivity(oldUnit, newUnit, getLoggedInUser(request));
        try {
            List<UnitHours> oldUnitOperationalHours = oldUnit.getOperationalHours();
            List<UnitHours> newUnitOperationalHours = newUnit.getOperationalHours();
            Collections.sort(oldUnitOperationalHours, (o1, o2) -> o1.getDayOfTheWeek().compareTo(o2.getDayOfTheWeek()));
            Collections.sort(newUnitOperationalHours, (o1, o2) -> o1.getDayOfTheWeek().compareTo(o2.getDayOfTheWeek()));
            for (int i = 0; i < oldUnitOperationalHours.size(); i++) {
                UnitHours data1 = oldUnitOperationalHours.get(i);
                UnitHours data2 = newUnitOperationalHours.get(i);
                if (!data1.getDeliveryOpeningTime().toString().substring(0,5).equals(data2.getDeliveryOpeningTime().toString().substring(0,5)) ||
                        !data1.getDeliveryClosingTime().toString().substring(0,5).equals(data2.getDeliveryClosingTime().toString().substring(0,5))) {
                    isBusinessHourChanges = true;
                }
            }
            ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext
                    .getBean("taskExecutor");
            boolean finalIsBusinessHourChanges = isBusinessHourChanges;
            executor.execute(() -> {
                if (finalIsBusinessHourChanges) {
                    try {
                        BusinessHourObject businessHourObject = new BusinessHourObject();
                        businessHourObject.setEvent(BusinessHourEvent.UNIT_UPDATED.value());
                        businessHourObject.setOutletId(oldUnit.getId());
                        businessHourObject.setOldBusinessHours(oldUnit.getOperationalHours());
                        WebServiceHelper.postRequestWithAuthInternalWithTimeout(env.getProperty("zomoto.business.hours.url"), businessHourObject, boolean.class, env.getProperty("cp.client.token"), 10, 10);
                    } catch (IOException e) {
                        LOG.info("Fail to call Zomato For Updating Business Hour");
                        LOG.info("Error : {}", e);
                    }
                }
            });
        }catch (Exception e){
            LOG.info("Error to Update Business Hours On zomato : {}",e);
        }
        int unitId = newUnit.getId();
        addUnitBrandMappings(request, unitId, unit.getBrands());
        return unitId;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/update/manager", produces = MediaType.APPLICATION_JSON)
    public int updateUnit(@RequestParam("unitId") String unitId,
                          @RequestParam("lastHandoverFrom")  String lastHandoverFrom,
                          @RequestParam("unitCafeManagerId")  String unitCafeManagerId,
                          @RequestParam("lastHandoverDate")  String lastHandoverDate, HttpServletRequest request)
            throws DataUpdationException, DataNotFoundException, IllegalAccessException, EmailGenerationException {
        LOG.info("Updating unit with Id {}", unitId);
        Integer unitIdInt = Integer.parseInt(unitId);
        Integer lastHandoverFromInt  = Integer.parseInt(lastHandoverFrom);
        Integer unitCafeManagerIdInt  = Integer.parseInt(unitCafeManagerId);
        Date lastHandoverDateD  = AppUtils.getDate(lastHandoverDate, "yyyy-MM-dd HH:mm:ss");
        Unit newUnit = masterService.getUnit(unitIdInt,false);
        newUnit.setUnitCafeManager(unitCafeManagerIdInt);
        newUnit.setLastHandoverFrom(lastHandoverFromInt.toString()+"_"+masterCache.getEmployeeBasicDetail(lastHandoverFromInt).getName());
        newUnit.setLastHandoverDate(lastHandoverDateD);
        Unit oldUnit = masterService.getUnit(unitIdInt, false);
        masterCacheImpl.addUnit(masterMetadataService.updateCacheForManager(newUnit));
        LOG.info("Updating unit with name {}", oldUnit.getName());

        try{
            LOG.info("Trying To Send Email Of Changes Done On Unit : {}",oldUnit.getName());
            List<String> toEmails = new ArrayList<>(Arrays.asList("<EMAIL>"));
            Unit mongoUnit = newUnit;
            Unit mongoUnitOld = oldUnit;

//            mongoUnit.setOperationalHours(null);//for mongodb entry only
//            mongoUnitOld.setOperationalHours(null);
            masterService.saveAuditLog(oldUnit.getId(), AuditChangeLogTypes.UNIT_DETAIL.value(), getLoggedInUser(request), mongoUnit,
                    AuditChangeLogTypes.UPDATE_ENTRY.value());

            loggerService.sendUnitChangeMail(mongoUnitOld, newUnit, masterCache.getEmployee(getLoggedInUser(request)), "UNIT", oldUnit.getId(),
                    toEmails, "Unit : " + oldUnit.getName());
        }catch (Exception e){
            LOG.info("Error While Sending Email Of Changes Done On Unit : {}",oldUnit.getName(),e);
        }
        loggerService.addActivity(oldUnit, newUnit, getLoggedInUser(request));
        ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext
                .getBean("taskExecutor");
        executor.execute(() -> {
            try {
                WebServiceHelper.postRequestWithAuthInternalWithTimeout(env.getProperty("channel.partner.cafe.timings.url"),new Object(),Boolean.class,env.getProperty("cp.client.token"));
            } catch (IOException e) {
                LOG.info("Error While Updating Channel Partner Cafe Timings Cache After Unit Update ::: {}" , e.getMessage());
            }
        });
        return newUnit.getId();
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/update/businesshour", produces = MediaType.APPLICATION_JSON)
    public boolean updateUnitBusinessHours(@RequestBody CafeTimingChangeRequest timingChangeRequest)
            throws DataUpdationException, DataNotFoundException {
        LOG.info("Updating Businees Hours of unit with name {}", timingChangeRequest.getUnitName());
        Unit oldUnit = masterService.getUnit(timingChangeRequest.getUnitId(),false);
        boolean isUpdateSuccessFull = unitService.updateUnitBusinessHours(timingChangeRequest);
        if(Objects.nonNull(timingChangeRequest.getDeliveryOpeningTime()) && Objects.nonNull(timingChangeRequest.getDeliveryClosingTime())) {
            Unit newUnit = masterService.getUnit(timingChangeRequest.getUnitId(), false);
            try {
                boolean isBusinessHourChanges = false;
                List<UnitHours> oldUnitOperationalHours = oldUnit.getOperationalHours();
                List<UnitHours> newUnitOperationalHours = newUnit.getOperationalHours();
                Collections.sort(oldUnitOperationalHours, (o1, o2) -> o1.getDayOfTheWeek().compareTo(o2.getDayOfTheWeek()));
                Collections.sort(newUnitOperationalHours, (o1, o2) -> o1.getDayOfTheWeek().compareTo(o2.getDayOfTheWeek()));
                for (int i = 0; i < oldUnitOperationalHours.size(); i++) {
                    UnitHours data1 = oldUnitOperationalHours.get(i);
                    UnitHours data2 = newUnitOperationalHours.get(i);
                    if (!data1.getDeliveryOpeningTime().toString().substring(0,5).equals(data2.getDeliveryOpeningTime().toString().substring(0,5)) ||
                            !data1.getDeliveryClosingTime().toString().substring(0,5).equals(data2.getDeliveryClosingTime().toString().substring(0,5))) {
                        isBusinessHourChanges = true;
                    }
                }
                ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext
                        .getBean("taskExecutor");
                boolean finalIsBusinessHourChanges = isBusinessHourChanges;
                executor.execute(() -> {
                    if (finalIsBusinessHourChanges) {
                        try {
                            BusinessHourObject businessHourObject = new BusinessHourObject();
                            businessHourObject.setEvent(BusinessHourEvent.UNIT_UPDATED.value());
                            businessHourObject.setOutletId(oldUnit.getId());
                            businessHourObject.setOldBusinessHours(oldUnit.getOperationalHours());
                            WebServiceHelper.postRequestWithAuthInternalWithTimeout(env.getProperty("zomoto.business.hours.url"), businessHourObject, boolean.class, env.getProperty("cp.client.token"), 10, 10);
                        } catch (IOException e) {
                            LOG.info("Fail to call Zomato For Updating Business Hour");
                            LOG.info("Error : {}", e);
                        }
                    }
                });
            }catch (Exception e){
                LOG.info("Error to Update Business Hours On zomato : {}",e);
            }
        }
        return isUpdateSuccessFull;
    }


    @RequestMapping(method = RequestMethod.POST, value = "unit/activate", produces = MediaType.APPLICATION_JSON)
    public boolean activateUnit(@RequestBody final int unit, HttpServletRequest request)
            throws DataUpdationException, DataNotFoundException, EmailGenerationException, IOException {
        LOG.info("Activating unit with unit Id {}", unit);
        Unit oldUnit = masterService.getUnit(unit, false);
        UnitStatusData oldData = new UnitStatusData(oldUnit.getId(), oldUnit.getStatus().name(), oldUnit.isLive());
        UnitStatusData newData = new UnitStatusData(unit, UnitStatus.ACTIVE.name(), false);
        Unit newUnit = unitService.changeUnitStatus(unit, UnitStatus.ACTIVE);
        unitService.changeUnitLiveStatus(unit, false);
        UnitActivationReceipt receipt = new UnitActivationReceipt("activate", newUnit, props.getBasePath());
        UnitActivationEmail email = new UnitActivationEmail(receipt, props);
        email.sendEmail();
        loggerService.addActivity(oldData, newData, getLoggedInUser(request));
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/deactivate", produces = MediaType.APPLICATION_JSON)
    public boolean deactivateUnit(@RequestBody final int unit, HttpServletRequest request)
            throws DataUpdationException, DataNotFoundException, IOException {
      return unitService.deactivateUnit(unit,getLoggedInUser(request));
    }

    @RequestMapping(method = RequestMethod.GET, value = "divisions", produces = MediaType.APPLICATION_JSON)
    public Collection<Division> allDivision() {
        LOG.info("Getting All Divisions ");
        return masterCache.getDivisionsValues();
    }

    @RequestMapping(method = RequestMethod.GET, value = "departments", produces = MediaType.APPLICATION_JSON)
    public Collection<Department> allDepartments() {
        LOG.info("Getting All Divisions ");
        return masterCache.getDepartmentsValues();
    }

    @RequestMapping(method = RequestMethod.GET, value = "designations", produces = MediaType.APPLICATION_JSON)
    public Collection<Designation> allDesignations() {
        LOG.info("Getting All Designations ");
        return masterCache.getDesignationsValues();
    }

    // TODO Put this is database.
    @RequestMapping(method = RequestMethod.GET, value = "regions", produces = MediaType.APPLICATION_JSON)
    public String[] allRegions() {
        LOG.info("Getting All Regions ");
        return masterCache.getAllRegions().toArray(new String[0]);
    }
    @RequestMapping(method = RequestMethod.GET, value = "all-cities", produces = MediaType.APPLICATION_JSON)
    public List<Location> allcities() {
        LOG.info("Getting All cities ");
        return masterCache.getActiveLocations();
    }

    @RequestMapping(method = RequestMethod.GET, value = "families", produces = MediaType.APPLICATION_JSON)
    public UnitCategory[] allUnitFamilies() {
        LOG.info("Getting All Unit Families");
        return UnitCategory.values();
    }

    @RequestMapping(method = RequestMethod.GET, value = "cafeType", produces = MediaType.APPLICATION_JSON)
    public CafeType[] allCafe(){
          LOG.info("Getting All CAFE TYPE SERVICE");
        return CafeType.values();
    }

    @RequestMapping(method = RequestMethod.GET, value = "sub-categories", produces = MediaType.APPLICATION_JSON)
    public UnitSubCategory[] allUnitSubCategories() {
        LOG.info("Getting All Unit Sub Categories");
        return UnitSubCategory.values();
    }

    @RequestMapping(method = RequestMethod.GET, value = "tax-profiles", produces = MediaType.APPLICATION_JSON)
    public List<TaxProfile> allTaxProfiles() {
        LOG.info("Getting All Tax Profiles");
        return masterCache.getTaxProfiles();
    }

    @RequestMapping(method = RequestMethod.GET, value = "tax-categories", produces = MediaType.APPLICATION_JSON)
    public Collection<TaxCategory> allTaxCategories() {
        LOG.info("Getting All Tax Categories");
        return taxCache.getAllTaxCategory();
    }

    @RequestMapping(method = RequestMethod.GET, value = "categories-tax-data", produces = MediaType.APPLICATION_JSON)
    public Collection<CategoryTax> allCategoriesTax() {
        LOG.info("Getting All Categories Tax");
        return taxCache.getAllCategoryTax();
    }

    // TODO Fraheem, Change this API call from UI
    @RequestMapping(method = RequestMethod.GET, value = "listTypes", produces = MediaType.APPLICATION_JSON)
    public Map<String, List<ListData>> getListTypes() throws DataNotFoundException {
        LOG.info("Getting All ListTypes for kettle ");
        return masterService.getAllListData();
    }

    @GetMapping(value = "listTypes-by-type", produces = MediaType.APPLICATION_JSON)
    public Map<String, List<ListData>> getListTypesByType(@RequestParam List<String> refTypes) throws DataNotFoundException {
        return masterService.getListTypesByType(refTypes);
    }


    // TODO Fraheem, Change this API call from UI
    @RequestMapping(method = RequestMethod.POST, value = "refLookUp/upsert", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public ListData upsertRefLookUp(@RequestBody final ListData request)
        throws DataUpdationException, DataNotFoundException {
        LOG.info("Upsert called on RefLookUp details for Category: {}", request.getDetail().getName());
        ListData data = masterService.upsertRefLookUp(request);
        masterCacheImpl.updateListData(data.getDetail().getType());
        return data;
    }

    // TODO Fraheem, Change this API call from UI

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     */
    @RequestMapping(method = RequestMethod.POST, value = "unit", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public Unit getUnit(@RequestBody final Integer unitId) {
        LOG.info("Getting Unit details for ID = {}", unitId);
        Unit unit = masterCache.getUnit(unitId);
        setHotspotFlag(unit);
        return unit;
    }

    private void setHotspotFlag(Unit unit) {
        try {
            String sysFlag = masterCache.getCacheReferenceMetadata(
                    CacheReferenceType.HOTSPOT_ENABLED_FOR_SYSTEM);
            if (Objects.nonNull(sysFlag) && sysFlag.equals(AppConstants.YES)) {
                unit.setHotspotEnabled(true);
            }
        } catch (Exception e) {
            LOG.error("Error while getting unit cache reference for hotspot ", e);
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "employee-meal-products", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public Collection<ProductVO> getEmployeeMealProducts(@RequestBody final Integer unitId) {
        LOG.info("Getting Employee Meal Products details for Unit Id: {}", unitId);
        // Employee Meal Product data can be changed using unit Id.
        // currently we are using single unit
        int mealUnitId = 0;
        for (UnitBasicDetail u : masterCache.getUnitsBasicDetails().values()) {
            if (UnitCategory.EMPLOYEE_MEAL.equals(u.getCategory())) {
                // meal unit can also be selected based on unit region
                mealUnitId = u.getId();
            }
        }
        return masterCache.getUnitProductTrimmedDetails(mealUnitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-products", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public UnitProductData getUnitProducts(@RequestBody final Integer unitId) {
        LOG.info("Getting Unit Products details for ID = {}", unitId);
        Unit unit = masterCache.getUnit(unitId);
        Collection<ProductVO> products = masterCache.getUnitProductTrimmedDetails(unitId);
        int brandId = AppConstants.CHAAYOS_BRAND_ID;
        int partnerId = AppConstants.DINE_IN_CHANNEL_PARTNER;
        int subscriptionProductId = props.getSubcriptionProductType();
        Map<MenuType, Map<Integer, List<ProductPriceVO>>> priceMap = masterCache.getUnitPriceMap(unitId, brandId, partnerId,subscriptionProductId);
        return getResult(unit, products, priceMap);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-dynamic-price", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public View getUnitPriceDetails(@RequestParam int unit) {
        Map<Unit,List<UnitMenuTypePrice>> output= new HashMap<>();
        int brandId = AppConstants.CHAAYOS_BRAND_ID;
        int partnerId = AppConstants.DINE_IN_CHANNEL_PARTNER;
//        for(Integer unit: unitId){
            Unit unitDetails=masterCache.getUnit(unit);
        int subscriptionProductId = props.getSubcriptionProductType();
            Map<MenuType, Map<Integer, List<ProductPriceVO>>> priceMap = masterCache.getUnitPriceMap(unit, brandId, partnerId,subscriptionProductId);
            List<UnitMenuTypePrice> priceDetails = masterCache.getUnitPriceDetails(unitDetails,priceMap);
            output.put(unitDetails,priceDetails);
//        }
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "Unit_Dynamic_Price_" + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                for(Map.Entry<Unit,List<UnitMenuTypePrice>> map: output.entrySet()){
                    writer.writeSheet(map.getValue(), UnitMenuTypePrice.class);
                }
            }
        };
    }

    @RequestMapping(method = RequestMethod.POST, value = "check-product-recipes", consumes = MediaType.APPLICATION_JSON)
    public boolean validUnitProductRecipes(@RequestBody final UnitProductRecipesKeys request) {
        LOG.info("checking unit product recipes for unit id = {}", request.getUnitId());
        return masterService.validUnitProductRecipes(request);
    }

    private UnitProductData getResult(Unit unit, Collection<ProductVO> products, Map<MenuType, Map<Integer, List<ProductPriceVO>>> priceMap) {
        UnitProductData result = new UnitProductData();
        Map<Integer,Boolean> milkSelectionPaidAddonMap  = props.getMilkSelectionPaidAddons();
        result.getProducts().addAll(new TreeSet<>(products));
        Map<Integer,List<Integer>>categoryType=new HashMap<>();
        if (unit.getLocation() != null) {
            int stateId = unit.getLocation().getState().getId();
            Set<String> codes = new HashSet<>();
            products.forEach(product -> {
                if (product.getTaxCode() == null) {
                    return;
                }
                if (!codes.contains(product.getTaxCode())) {
                    TaxData tax = taxCache.getTaxData(stateId, product.getTaxCode());
                    if (tax != null && tax.getState() != null) {
                        result.getTaxes().add(new TaxDataVO(tax));
                    }
                    codes.add(product.getTaxCode());
                }
                //CATEGORY MAP WITH BRAND ID
                if(!categoryType.containsKey(product.getBrandId())){
                    categoryType.put(product.getBrandId(),new ArrayList<>());
                }
               if(categoryType.values().stream().noneMatch(list -> list.contains(product.getType()))){
                   categoryType.get(product.getBrandId()).add(product.getType());
               }
                if (!CollectionUtils.isEmpty(product.getPrices())) {
                    product.getPrices().forEach(price -> {
                        if (Objects.nonNull(product.getProductAliasName())) {
                            price.setAliasProductName(product.getProductAliasName());
                       }
                        if (price.getRecipe() != null && price.getRecipe().getOptions() != null) {
                            price.getRecipe().getOptions().forEach(option -> {
                                    if (milkSelectionPaidAddonMap.containsKey(option.getId())) {
                                        option.setMilkSelectionVariant(milkSelectionPaidAddonMap.get(option.getId()));
                                    }
                            });
                        }
                   });
               }

            });
        }
        result.setCategoryMap(categoryType);
        result.setPriceMap(priceMap);
        return result;
    }

    //TODO - AAshutosh to check if this is called else mark it as deprecated
    @RequestMapping(method = RequestMethod.GET, value = "region/products", produces = MediaType.APPLICATION_JSON)
    public UnitProductData getRegionProductProfile(@RequestParam("region") final String region,
                                                   @RequestParam("unitId") final int unitId,
                                                   @RequestParam("partnerOrder") final boolean partnerOrder) {
        LOG.info("region selected is :::::::::::::::::::::::::::::::::: {}", region);
        List<UnitBasicDetail> codUnits = masterCache.getUnits(UnitCategory.COD);
        Optional<UnitBasicDetail> unit = codUnits.stream().filter(unit1 -> region.equals(unit1.getRegion()) && partnerOrder == unit1.isPartnerPriced()).findAny();
        Collection<ProductVO> products = unit.isPresent() ? masterCache.getUnitProductTrimmedDetails(unit.get().getId())
            : Collections.emptyList();
        int brandId = AppConstants.CHAAYOS_BRAND_ID;
        int partnerId = AppConstants.CHAAYOS_DELIVERY_PARTNER_ID;
        int subscriptionProductId = props.getSubcriptionProductType();
        Map<MenuType, Map<Integer, List<ProductPriceVO>>> priceMap = masterCache.getUnitPriceMap(unitId, brandId, partnerId,subscriptionProductId);
        return getResult(masterCache.getUnit(unitId), products, priceMap);
    }

    @RequestMapping(method = RequestMethod.GET, value = "brand/partner/products", produces = MediaType.APPLICATION_JSON)
    public UnitProductData getRegionProductProfile(@RequestParam("brandId") final Integer brandId,
                                                   @RequestParam("unitId") final int unitId,
                                                   @RequestParam("partnerId") final Integer partnerId) {
        List<UnitBasicDetail> codUnits = masterCache.getUnits(UnitCategory.COD);
        Integer priceProfileUnitId = unitService.getPriceProfileUnitId(brandId, unitId, partnerId);
        Optional<UnitBasicDetail> unit = codUnits.stream().filter(unit1 -> priceProfileUnitId.equals(unit1.getId())).findAny();
        Collection<ProductVO> products = unit.isPresent() ? masterCache.getUnitProductTrimmedDetails(unit.get().getId())
            : Collections.emptyList();
        int subscriptionProductId = props.getSubcriptionProductType();
        Map<MenuType, Map<Integer, List<ProductPriceVO>>> priceMap = masterCache.getUnitPriceMap(unitId, brandId, partnerId,subscriptionProductId);
        UnitProductData data = getResult(masterCache.getUnit(unitId), products, priceMap);
        return unitService.setProductAliases(data, brandId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit/partner/products/trimmed", produces = MediaType.APPLICATION_JSON)
    public List<ProductBasicDetail> getRegionPartnerProductProfile(@RequestParam("brandId") final Integer brandId,
                                                                   @RequestParam("unitId") final int unitId,
                                                                   @RequestParam("partnerId") final Integer partnerId) {
        List<UnitBasicDetail> codUnits = masterCache.getUnits(UnitCategory.COD);
        Integer priceProfileUnitId = unitService.getPriceProfileUnitId(brandId, unitId, partnerId);
        Optional<UnitBasicDetail> unit = codUnits.stream().filter(unit1 -> priceProfileUnitId.equals(unit1.getId())).findAny();
        Collection<ProductVO> products = unit.isPresent() ? masterCache.getUnitProductTrimmedDetails(unit.get().getId())
            : Collections.emptyList();
        List<ProductBasicDetail> productList = new ArrayList<>();
        products.forEach(productVO ->
            productList.add(MasterDataConverter.convert(productVO))
        );
        return productList;
    }


    @RequestMapping(method = RequestMethod.POST, value = "partner/products/trimmed", produces = MediaType.APPLICATION_JSON)
    public List<ProductBasicDetail> getRegionPartnerProductProfile(@RequestBody List<Integer> pricingUnitIds) throws DataNotFoundException {
        Set<ProductVO> codProductList = new HashSet<>();
        if(CollectionUtils.isEmpty(pricingUnitIds)){
            throw new DataNotFoundException("Pricing Unit List Is Empty");
        }
        pricingUnitIds.forEach(pricingUnitId -> {
            Collection<ProductVO> products = masterCache.getUnitProductTrimmedDetails(pricingUnitId);
            if(!products.isEmpty()){
                products = products.stream().filter(productVO -> !CollectionUtils.isEmpty(productVO.getPrices())).
                        collect(Collectors.toList());
                codProductList.addAll(products);
            }
        });
        List<ProductBasicDetail> productList = new ArrayList<>();
        codProductList.forEach(productVO ->
                productList.add(MasterDataConverter.convert(productVO))
        );
        return productList;
    }

    @RequestMapping(method = RequestMethod.POST, value = "metadata/android", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public TransactionMetadata getAllProductCategoryMetadata(@RequestBody final Integer unitId)
        throws DataNotFoundException {
        LOG.info("Getting Request Android-Metadata from Unit with ID : {}", unitId);
        return masterService.getTransactionData(true);
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "metadata", consumes = MediaType.APPLICATION_JSON,
        produces = MediaType.APPLICATION_JSON)
    public TransactionMetadata getMetadata(@RequestBody final Integer unitId)
        throws DataNotFoundException {
        LOG.info("Getting Request Metadata from Unit with ID : {}", unitId);
        return masterService.getTransactionData(false);
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws AuthenticationFailureException
     * @throws DataNotFoundException
     * @use used in scm to get product categories
     */
    @RequestMapping(method = RequestMethod.POST, value = "metadata-categories", consumes = MediaType.APPLICATION_JSON,
        produces = MediaType.APPLICATION_JSON)
    public TransactionMetadata getMetadataCategories(@RequestBody final Integer unitId)
        throws DataNotFoundException {
        LOG.info("Getting Request Metadata categories from Unit with ID : {}", unitId);
        return masterService.getMetadataCategories();
    }

    @RequestMapping(method = RequestMethod.POST, value = "cafe-adhoc-expense")
    public ListData getCafeAdhocExpense(@RequestBody final String code) throws DataNotFoundException {
        LOG.info("Getting ref lookup entry code : {}", code);
        return masterService.getListData(code, true);
    }

    @RequestMapping(method = RequestMethod.POST, value = "monk-configuration/pi/add", consumes = MediaType.TEXT_HTML,
        produces = MediaType.APPLICATION_JSON)
    public MonkConfiguration addMonkConfiguration(@RequestBody final String str)
        throws DataNotFoundException, AuthenticationFailureException {
        MonkConfiguration configuration = JSONSerializer.toJSON(str, MonkConfiguration.class);
        LOG.info("POST request for saving monk configurations : " + configuration.getUnitId());
        return unitService.addMonkConfiguration(configuration);
    }

    @RequestMapping(method = RequestMethod.POST, value = "activate/suggest/wallet", produces = MediaType.APPLICATION_JSON)
    public boolean setSuggestWalletStatus(@RequestBody final CacheReferenceMetadata flag){
        LOG.info("Activate Deactivate  Suggest Wallet Status");
        return unitService.setSuggestWalletStatus(flag);
    }

    @RequestMapping(method = RequestMethod.POST, value = "monk-configuration/add", consumes = {
        MediaType.APPLICATION_JSON, MediaType.TEXT_HTML}, produces = MediaType.APPLICATION_JSON)
    public MonkConfiguration addMonkConfiguration(@RequestBody final MonkConfiguration configuration) {
        LOG.info("POST request for saving monk configurations : {}", configuration.getUnitId());
        return unitService.addMonkConfiguration(configuration);
    }

    @RequestMapping(method = RequestMethod.GET, value = "monk-configuration/metadata")
    public List<MonkAttr> getMonkConfigurations() {
        LOG.info(":::: GET request for getting monk metadata configurations :::: ");
        return unitService.getMonkMetadata();
    }

    @RequestMapping(method = RequestMethod.GET, value = "monk-configuration/conf-data")
    public List<MonkConfigurationValue> getUnitMonkConfigurationData(@RequestParam int unitId) {
        LOG.info(":::: GET request for getting monk conf data of unit id  :::: {}", unitId);
        return unitService.getUnitMonkConfigurationData(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit/change-unit-live-status")
    public boolean changeUnitLiveStatus(@RequestParam int unitId, @RequestParam boolean status, @RequestParam boolean dineIn,
                                        @RequestParam boolean chaayos, HttpServletRequest request)
        throws DataNotFoundException, DataUpdationException {
        Unit oldUnit = masterService.getUnit(unitId, false);
        if(status && oldUnit.getFssai()==null){
            return false;
        }

        UnitStatusData oldData = new UnitStatusData(oldUnit.getId(), oldUnit.getStatus().name(), oldUnit.isLive());
        UnitStatusData newData = new UnitStatusData(unitId, oldUnit.getStatus().name(), status);
        unitService.changeUnitLiveStatus(unitId, status);
        if(status && oldUnit.getCloneUnitId()!=null){
            try {
                unitService.cloneDayCloseDataForUnit(unitId, oldUnit.getCloneUnitId());
            }catch(Exception e){
                LOG.error("Error while cloning scm ordering data: {}",e,unitId);
            }
        }
        if (oldUnit.getFamily().equals(UnitCategory.CAFE)) {
            unitService.changeUnitStatusForDineInAndChaayos(unitId, dineIn, chaayos);
        }
        loggerService.addActivity(oldData, newData, getLoggedInUser(request));
        return true;
    }


    @RequestMapping(method = RequestMethod.GET, value = "area-manager-units", produces = MediaType.APPLICATION_JSON)
    public List<IdNameCategory> getAllUnitsOfAM(@RequestParam final Integer amId) {
        LOG.info("Getting All units for area manager {}", amId);
        List<Unit> units = masterService.getUnitsOfAreaManager(amId);

        List<IdNameCategory> all = new ArrayList<>();
        if (units != null) {
            for (Unit detail : units) {
                if (detail.getStatus().equals(UnitStatus.ACTIVE)) {
                    IdNameCategory data = new IdNameCategory();
                    BeanUtils.copyProperties(detail, data);
                    all.add(data);
                }
            }
        }
        return all;
    }


    @RequestMapping(method = RequestMethod.GET, value = "cafe-manager-units", produces = MediaType.APPLICATION_JSON)
    public List<IdNameCategory> getAllUnitsOfCM(@RequestParam final Integer amId) {
        LOG.info("Getting All units for area manager  {}", amId);
        List<Unit> units = masterService.getUnitsOfCafeManager(amId);

        List<IdNameCategory> all = new ArrayList<>();
        if (units != null) {
            for (Unit detail : units) {
                if (detail.getStatus().equals(UnitStatus.ACTIVE)) {
                    IdNameCategory data = new IdNameCategory();
                    BeanUtils.copyProperties(detail, data);
                    all.add(data);
                }
            }
        }
        return all;
    }

    @RequestMapping(method = RequestMethod.GET, value = "all-active-units-sub-category", produces = MediaType.APPLICATION_JSON)
    public List<IdNameCategory> getAllActiveUnitsBySubCategory(@RequestParam("category") final String category,
                                                               @RequestParam("subCategory") final String subCategory) {
        LOG.info("Getting All units for a category: {} and sub category : {}", category, subCategory);
        List<UnitBasicDetail> units = null;
        units = getUnits(category, null);
        UnitSubCategory unitSubCategory = UnitSubCategory.valueOf(subCategory);
        List<IdNameCategory> all = new ArrayList<>();
        if (units != null) {
            for (UnitBasicDetail detail : units) {
                if (detail.getStatus().equals(UnitStatus.ACTIVE) && detail.getSubCategory().equals(unitSubCategory)) {
                    IdNameCategory data = new IdNameCategory();
                    BeanUtils.copyProperties(detail, data);
                    all.add(data);
                }
            }
        }
        return all;
    }


    @RequestMapping(method = RequestMethod.GET, value = "unit/payment-modes", produces = MediaType.APPLICATION_JSON)
    public List<PaymentMode> getPaymentModesOfUnit(@RequestParam final Integer unitId) {
        LOG.info("Getting All payment modes for unit {}", unitId);
        List<Integer> unitPaymentModes = masterCache.getUnit(unitId).getPaymentModes();
        List<PaymentMode> paymentModes = new ArrayList<>();
        unitPaymentModes.forEach(paymentModeId ->
            paymentModes.add(masterCache.getPaymentModes().get(paymentModeId))
        );
        return paymentModes;
    }

    private List<UnitBasicDetail> getUnits(String category, Boolean excludeTestingUnit) {
        List<UnitBasicDetail> filteredUnits = new ArrayList<>();

        if (AppConstants.ALL.equalsIgnoreCase(category)) {
            filteredUnits = masterCache.getAllUnits();
            return filterUnitsByCompanyOrBrand(filteredUnits);
        } else {
            try {
                if(AppConstants.TESTING_UNIT.equals(category)) {
                    filteredUnits = unitService.getAllTestingUnits(category);
                } else {
                    List<UnitBasicDetail> unitBasicDetails = masterCache.getUnits(UnitCategory.valueOf(category));
                    List<UnitBasicDetail> units = new ArrayList<>();
                    if (excludeTestingUnit != null && excludeTestingUnit) {
                        for(UnitBasicDetail unit : unitBasicDetails) {
                            if(!unit.getIsTestingUnit()){
                                units.add(unit);
                            }
                        }
                        filteredUnits = units;
                    } else {
                        filteredUnits = unitBasicDetails;
                    }
                }

                filteredUnits = filterUnitsByCompanyOrBrand(filteredUnits);

                return filteredUnits;
            } catch (IllegalArgumentException e) {
                LOG.error(e.getMessage());
            }
        }
        return new ArrayList<>();
    }

    private List<UnitBasicDetail> filterUnitsByCompanyOrBrand(List<UnitBasicDetail> units) {
        if (RequestContext.isContextAvailable()) {
            List<Integer> mappedUnits = MasterUtil.getMappedUnits();
            units = units.stream().filter(u -> mappedUnits.contains(u.getId())).collect(Collectors.toList());
        }
        return units;
    }

    private List<UnitBasicDetail> getUnitsByRegion(String category, String region, Boolean excludeTestingUnit) {
        List<UnitBasicDetail> units = getUnits(category, excludeTestingUnit);
        List<UnitBasicDetail> filteredUnits = null;
        if (units != null) {
            if (region != null && !region.isEmpty()) {
                Set<String> regions = new HashSet<>(Arrays.asList(region.split(",")));
                filteredUnits = new ArrayList<>();
                for (UnitBasicDetail unit : units) {
                    if (regions.contains(unit.getRegion())) {
                        filteredUnits.add(unit);
                    }
                }
                return filteredUnits;
            }
        }
        return units;
    }

    @RequestMapping(method = RequestMethod.GET, value = "update-handover-Date", produces = MediaType.APPLICATION_JSON)
    public boolean updateHandOverDate(@RequestParam("handOverData") final String handOverData, @RequestParam("unitId") final Integer unitId) throws ParseException, DataNotFoundException {
        boolean status = masterService.updateHandOverDateForUnit(handOverData, unitId);
        Unit data = masterService.getUnit(unitId, true);
        masterCacheImpl.addUnit(data);
        return status;
    }

    @RequestMapping(method = RequestMethod.GET, value = "update-subscription-info", produces = MediaType.APPLICATION_JSON)
    public boolean updateSubscriptionProductInfo(){
        return masterCacheImpl.addSubscriptionProduct(masterCache.getAllProducts());
    }

    @RequestMapping(method = RequestMethod.GET, value = "brand-partner-mapping", produces = MediaType.APPLICATION_JSON)
    public List<BrandMapping> getPartnerMappingUnits(@RequestParam Integer brandId) {
        return unitService.getPartnerMappingUnits(brandId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "enable-unit-f9")
//    @ResponseStatus(value = HttpStatus.OK)
    public void enableUnitF9(@RequestParam Integer unitId) {
        LOG.info("Request received to update the F9 Enabled flag for unit : {}",masterCache.getUnit(unitId).getName());
        unitService.updateUnitF9Flag(unitId);
    }

    /*@RequestMapping(method = RequestMethod.POST, value = "apps/check-app-version")
    @ResponseBody
    public String getUnitMonkApkVersionOld(@RequestBody Map<String,Object> version)
            throws DataNotFoundException, AuthenticationFailureException {
        AppBuildVersionData apkVersion = new AppBuildVersionData();
        apkVersion.setVersion((int) version.get("currentVersion"));
        apkVersion.setAppName(getAppName(version.get("appName"),apkVersion.getVersion()));
        apkVersion.setUnitId((int) version.get("unitId"));
        LOG.info(":::: GET request for getting monk conf data of unit id  :::: " + apkVersion.getUnitId());
        return convertToOldObject(appsManagementService.checkAndReturnVersion(apkVersion));
    }


    private String convertToOldObject(AppBuildVersionData apkVersion){
        Map<String,Object> oldObject = new HashMap();
        oldObject.put("appName", apkVersion.getAppName());
        oldObject.put("currentVersion", apkVersion.getVersion());
        oldObject.put("unitId", apkVersion.getUnitId());
        oldObject.put("url", apkVersion.getUrl());
        return JSONSerializer.toJSON(oldObject);
    }

    private String getAppName(Object appName, int version) {
        String name = (String) appName;
        StringBuffer returnName = new StringBuffer(props.getEnvironmentType().toString());
        if(name.toLowerCase().contains("com.chaayos.kettle.android_phase4")){
            returnName.append("_Assembly screen app V4_Android-");
        }else if(name.toLowerCase().contains("Workstation with monk V4")){
            returnName.append("_Workstation with monk V4_Android-");
        }else if(name.toLowerCase().contains("Workstation V4")){
            returnName.append("_Workstation V4_Android-");
        }
        returnName.append(version).append(".apk");
        return returnName.toString();
    }*/
    @RequestMapping(method = RequestMethod.POST, value = "add-partner-edc--mapping", produces = MediaType.APPLICATION_JSON)
    public boolean addPartnerEdcMapping(@RequestBody UnitToPartnerEdcMappingDetail unitToPartnerEdcMappingDetail)
            throws DataNotFoundException, DataUpdationException {
            LOG.info("Getting Request For Adding Partner Edc Mapping List For {} with {} " ,unitToPartnerEdcMappingDetail.getUnitBasicDetail().getName(),unitToPartnerEdcMappingDetail.getPartnerName());
            return unitService.addPartnerEdcMapping(unitToPartnerEdcMappingDetail);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-partner-edc-mapping-list", produces = MediaType.APPLICATION_JSON)
    public List<UnitToPartnerEdcMapping> getPartnerEdcMappingList(@RequestParam String status)
        throws DataNotFoundException, DataUpdationException{

        try {
            LOG.info("Getting Request For Partner Edc Mapping List For {} users" ,status);
            List<UnitToPartnerEdcMapping> unitToPartnerEdcMappings = new ArrayList<>();
            if (status.equals(AppConstants.ALL)) {
                unitToPartnerEdcMappings.addAll(unitService.getPartnerEdcMappingList(AppConstants.ACTIVE));
                unitToPartnerEdcMappings.addAll(unitService.getPartnerEdcMappingList(AppConstants.IN_ACTIVE));
            } else {
                unitToPartnerEdcMappings.addAll(unitService.getPartnerEdcMappingList(status));
            }
            return unitToPartnerEdcMappings;
        }
        catch (Exception e){
            LOG.info("Error while Getting the list Of partnerEdcMapper", e);
            return Collections.emptyList();
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-partner-edc-mapping-list", produces = MediaType.APPLICATION_JSON)
    public boolean updatePartnerEdcMappingList(@RequestBody List<UnitToPartnerEdcMappingDetail> unitToPartnerEdcMappingDetails)
        throws DataNotFoundException, DataUpdationException{

        LOG.info("Getting Request For Updating Partner Edc Mapping List For {}  " ,unitToPartnerEdcMappingDetails);


        return unitService.updatePartnerEdcMapping(unitToPartnerEdcMappingDetails);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-ip-address", produces = MediaType.APPLICATION_JSON)
    public boolean updateIpAddressForUnit(@RequestBody UnitIpAddressRequest unitIpAddressRequest) {
        try {
            return unitService.updateIpAddressForUnit(unitIpAddressRequest);
        } catch (Exception e) {
            LOG.error("Error while updating ip address", e);
            return false;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-ip-address", produces = MediaType.APPLICATION_JSON)
    public UnitIpAddressData getIpAddressForUnit(@RequestBody UnitIpAddressRequest unitIpAddressRequest) {
        return unitService.getIpAddressForUnit(unitIpAddressRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-unit-version-detail", produces = MediaType.APPLICATION_JSON)
    public Map<Integer,List<ApplicationVersionDetailData>> getUnitVersionMapping() {
        return unitService.getUnitVersionMapping();
    }

    @RequestMapping(method = RequestMethod.POST, value = "create-update-version-event", produces = MediaType.APPLICATION_JSON)
    public Integer createUpdateVersionEvent(@RequestBody List<VersionEventDomain> eventDomains) {
        return unitService.createUpdateVersionEvent(eventDomains);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-active-cafe-pos-version", produces = MediaType.APPLICATION_JSON)
    public Map<String,List<String>> getPosCafeVersions(){
        try {
            return unitService.getAllActiveApplicationVersion();
        }catch (Exception e){
            LOG.info("Error in getting active cafe-pos version");
            return null;
        }
    }

    @RequestMapping(method = RequestMethod.POST,value = "update-cafe-pos-version",produces = MediaType.APPLICATION_JSON)
    public void updateCafePosVesrion(@RequestParam @Nullable String applicationName,@RequestParam @Nullable String applicationVersion,
                                     @RequestParam Integer updatedBy,@RequestParam @Nullable String releaseType ,
                                     @RequestParam @Nullable String buildName ,
                                     @RequestParam @Nullable String deploymentDescription){
        try{
            unitService.updateCafePosVersion(applicationName,applicationVersion,updatedBy,releaseType,buildName,deploymentDescription);
        }
        catch (Exception e){
            LOG.info("Error in updating Cafe-pos version");
        }
    }


    @RequestMapping(method = RequestMethod.GET, value = "get-all-unit-version-event", produces = MediaType.APPLICATION_JSON)
    public Map<Integer,List<ApplicationVersionEvent>> getAllUnitVersionEvent() {
        return unitService.getAllUnitVersionEvent();
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-apk-build",
            produces = MediaType.APPLICATION_JSON, consumes = MediaType.MULTIPART_FORM_DATA)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ApkUploadResponse uploadApkBuild(HttpServletRequest request,
                                            @RequestParam(value = "file") final MultipartFile file) {
        LOG.info("Request to upload Apk Build");
        return unitService.uploadApkBuild(file, props.getS3CdnServiceBucket());
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-apk-build",
            produces = MediaType.TEXT_PLAIN)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String getApkBuild(HttpServletResponse response,@RequestParam String applicationName ,@RequestParam String applicationVersion) throws IOException {
        LOG.info("Request to Get Apk Build");
        return unitService.getApkBuild(response,applicationName,applicationVersion);

    }

    @RequestMapping(method = RequestMethod.POST,value = "add-version-compatibility",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void addApplicationCompatibility(@RequestParam String applicationName,@RequestParam String applicationVersion,
                                            @RequestParam String posVersion,@RequestParam Integer updatedBy){
        LOG.info("Request To Add Version Compatibility");
        unitService.addVersionCompatability(applicationName,applicationVersion,posVersion,updatedBy);
    }

    @RequestMapping(method = RequestMethod.GET,value = "get-compatible-Version",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<String> getCompatibleVersions(@RequestParam String applicationName,@RequestParam String posVersion){
        LOG.info("Request to get Compatible Application Version");
        return unitService.getCompatibleApplicationVersion(applicationName,posVersion);
    }

    @RequestMapping(method = RequestMethod.POST,value = "get-unit-versions",produces = MediaType.APPLICATION_JSON)
    public Map<String,UnitVersionDetail> getUnitVersions(@RequestBody UnitVersionDomain domain){
        LOG.info("Request To get unit versions");
        return unitService.getUnitVersions(domain);
    }

    @RequestMapping(method = RequestMethod.POST , value = "get-application-version",produces = MediaType.APPLICATION_JSON)
    public List<ApplicationVersionDetail> getApplicationVersionDetail(@RequestBody List<ApplicationVersionDomian> applicationVersionList){
        LOG.info("Request to get Application Version Details");
        return unitService.getApplicationVersionDetail(applicationVersionList);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-compatible-pos-version",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<String> getCompatiblePosVersion(@RequestParam String applicationName,@RequestParam String applicationVersion){
        return unitService.getCompatiblePosVersion(applicationName,applicationVersion);
    }

    @RequestMapping(method = RequestMethod.GET , value="listTypes/delayReasons" ,produces = MediaType.APPLICATION_JSON)
    public List<String> getListTypesForDelayReason() throws DataNotFoundException{
        LOG.info("Getting Delay Reasons for kettle");
        return masterService.getDelayReason();
    }


    // UNIT CLOSURE INITIATE CALL
    @PostMapping(value = "/closure/initiate", consumes = MediaType.APPLICATION_JSON)
    public boolean initiateUnitClosure(@RequestBody UnitClosureEventDomain unitClosureEventDomain) throws MasterException, EmailGenerationException {
        return unitClosureService.initiateUnitClosure(unitClosureEventDomain);
    }

    //UNIT CLOSURE GET CALL BY STATUS
    @GetMapping(value = "/closure/get-by-status")
    public List<UnitClosureEventDomain> getClosureByStatus(@RequestParam String closureStatus) throws MasterException {
        return unitClosureService.getClosureByStatus(closureStatus);
    }

    //UNIT CLOSURE GET FOR STATE
    @GetMapping(value = "/closure/get-by-state")
    public List<UnitClosureStateEventDomain> getClosureByState(@RequestParam Long requestId) throws MasterException {
        return unitClosureService.getClosureByState(requestId);
    }

    //UNIT CLOSURE STATE CLOSING
    @PostMapping(value = "/closure/close-state")
    public boolean closeStateEvent(@RequestBody UnitClosureStateEventDomain unitClosureStateEventDomain) throws MasterException {
        return unitClosureService.closeStateEvent(unitClosureStateEventDomain);
    }

    //UNIT CLOSURE STATE CLOSING
    @PostMapping(value = "/closure/close-event")
    public boolean closeEvent(@RequestBody UnitClosureEventDomain unitClosureEventDomain) throws MasterException {
        return unitClosureService.closeEvent(unitClosureEventDomain);
    }

    @GetMapping(value = "/get-closure-status")
    public UnitClosureStateEnum[] getClosureStatus() {
        return UnitClosureStateEnum.values();
    }

    @GetMapping(value = "/get-closure-form-metadata")
    public List<UnitClosureFormDataDomain> getClosureFormMetaData(@RequestParam Integer unitId){
        return  unitClosureService.getUnitClosureFormMetaData(unitId);
    }
    @PostMapping(value = "/add-closure-form-metadata")
    public Boolean addClosureFormMetaData(@RequestBody List<UnitClosureFormMetaDataDomain> unitClosureFormMetaData){
         return  unitClosureService.addUnitClosureFormMetadata(unitClosureFormMetaData);
    }
    @PostMapping(value = "/add-unit-closure-form-data")
     public Boolean addUnitClosureFormData(@RequestBody List<UnitClosureFormDataDomain> unitClosureFormDataDomain, @RequestParam Long  unitClosureEventId,@RequestParam Boolean isSaved, HttpServletRequest request) throws MasterException {
            return unitClosureService.addUnitClosureFormData(unitClosureFormDataDomain, unitClosureEventId, getLoggedInUser(request),isSaved);
    }
    @Scheduled(cron = "0 45 4 * * *", zone = "GMT+05:30")
    public void initiateOperationCloseCron() throws IOException {
       unitClosureService.initiateOperationClose();
    }

    @GetMapping(value = "/initiate-operation-close")
    public String initiateOperationClose() throws IOException {
        return  unitClosureService.initiateOperationClose();
    }
    @GetMapping(value = "/closure/get-by-unitId")
    public List<UnitClosureStateEventDomain> getClosureStateByUnitID(@RequestParam Integer unitId) throws MasterException {
        return unitClosureService.getStateFromUnitId(unitId);
    }

    @GetMapping(value = "/send-unit-closure-task-done-notification")
    public void sendUnitClosureTaskDoneNotification(@RequestParam Long eventId) throws MasterException, EmailGenerationException {
         unitClosureService.sendUnitClosureTaskDoneNotification(eventId);
    }

    @PostMapping(value = "update-feedback-questions")
    public boolean updateFeedbackQuestions(@RequestBody FeedbackQuestionDomain domain){
        return unitService.updateFeedbackQuestions(domain);
    }

    @GetMapping(value = "get-feedback-questions")
    public List<FeedbackQuestionsDetail> getFeedbackQuestionDetail(@RequestParam String questionType){
        return unitService.getAllFeedBackQuestion(questionType);
    }

    @PostMapping(value = "delete-feedback-question")
    public boolean deleteFeedbackQuestion(@RequestParam Integer questionId){
        return unitService.deleteFeedBackQuestion(questionId);
    }

    @GetMapping(value = "get-feedback-unit-mapping")
    public List<FeedbackQuestionsUnitMapping> getAllFeedbackUnitMapping(@RequestParam Integer questionId){
        return unitService.getAllFeedbackQuestionUnitMapping(questionId);
    }

    @PostMapping(value = "add-feedback-unit-mapping")
    public boolean addFeedbackQuestionUnitMapping(@RequestBody FeedbackQuesMappingDomain request){
        return unitService.addFeedbackUnitMapping(request);
    }

    @PostMapping(value = "update-feedback-unit-mapping")
    public boolean updateFeedbackQuestionUnitMapping(@RequestParam Integer mappingId,@RequestParam Integer updatedBy){
        return unitService.updateFeedbackUnitMapping(mappingId,updatedBy);
    }

    @PostMapping(value = "change-question-status")
    public boolean changeQuestionStatus(@RequestParam Integer questionId,@RequestParam Integer updatedBy){
        return unitService.changeQuestionStatus(questionId,updatedBy);
    }

    @PostMapping(value = "bulk-update-feedback-unit-mapping")
    public boolean bulkUpdateFeedbackUnitMapping(@RequestBody FeedbackQuesMappingDomain domain){
        return unitService.bulkUpdateFeedbackUnitMapping(domain);
    }
    @PostMapping(value = "add-unit-contacts")
    public boolean addUnitContacts(@RequestBody UnitContactDetails unitContactDetails) {
        return unitService.addUnitContacts(unitContactDetails);
    }

    @GetMapping(value = "/send-unit-closure-pending-task-notification")
    public void sendUnitClosurePendingTaskNotification(@RequestParam Integer unitId) throws MasterException, EmailGenerationException {
        unitClosureService.sendPendingTaskNotification(unitId);
    }

    @PostMapping(value = "/suspend-unit-closure-event")
    public Boolean suspendUnitClosureEvent(@RequestBody Integer unitId) throws MasterException{
     return  unitClosureService.suspendUnitClosureEvent(unitId);
    }
    @RequestMapping(method = RequestMethod.GET , value="listTypes/compensationReason" ,produces = MediaType.APPLICATION_JSON)
    public List<String> getListTypesForCompensationReason() throws DataNotFoundException{
        LOG.info("Getting Compensation Reasons");
        return masterService.getCompensationReason();
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-partner-dqr-mapping-list", produces = MediaType.APPLICATION_JSON)
    public List<UnitToPartnerDqrMapping> getPartnerDqrMappingList(@RequestParam String status){
        try {
            LOG.info("Getting Request For Partner dqr Mapping List For {} users" ,status);
            List<UnitToPartnerDqrMapping> unitToPartnerDqrMappings = new ArrayList<>();
            if (status.equals(AppConstants.ALL)) {
                unitToPartnerDqrMappings.addAll(unitService.getPartnerDqrMappingList(AppConstants.ACTIVE + "_" + AppConstants.IN_ACTIVE));
            } else {
                unitToPartnerDqrMappings.addAll(unitService.getPartnerDqrMappingList(status));
            }
            return unitToPartnerDqrMappings;
        }
        catch (Exception e){
            LOG.info("Error while Getting the list Of partnerEdcMapper", e);
            return Collections.emptyList();
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-partner-dqr-mapping", produces = MediaType.APPLICATION_JSON)
    public boolean addPartnerDqrMapping(@RequestBody UnitToPartnerDqrMapping unitToPartnerDqrMapping)
            throws DataNotFoundException, DataUpdationException {
        LOG.info("Getting Request For Adding Partner Dqr Mapping List For {} with {} " ,unitToPartnerDqrMapping.getUnitId(),unitToPartnerDqrMapping.getPartnerName());
        return unitService.addPartnerDqrMapping(unitToPartnerDqrMapping);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-partner-dqr-mapping-list", produces = MediaType.APPLICATION_JSON)
    public boolean updatePartnerDqrMappingList(@RequestBody List<UnitToPartnerDqrMapping> unitToPartnerDqrMappings)
            throws DataNotFoundException, DataUpdationException{
        LOG.info("Getting Request For Updating Partner Dqr Mapping List For {}  " ,unitToPartnerDqrMappings);
        return unitService.updatePartnerDqrMapping(unitToPartnerDqrMappings);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-unit-ws-station-category-mapping", produces = MediaType.APPLICATION_JSON)
    public UnitWsToStationCategoryMappingRequest getUnitWsToStationCategoryMapping(@RequestParam Integer unitId) {
        return unitService.getUnitWsToStationCategoryMapping(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-all-stations", produces = MediaType.APPLICATION_JSON)
    public Set<String> getAllStations() {
        return unitService.getAllStations();
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-unit-ws-station-category-mapping", produces = MediaType.APPLICATION_JSON)
    public boolean addUnitWsToStationCategoryMapping(@RequestBody UnitWsToStationCategoryMappingRequest wsToStationCategoryMapping) {
        return unitService.addUnitWsToStationCategoryMapping(wsToStationCategoryMapping);
    }

    @PostMapping("sync-unit-product-price")
    public boolean syncUnitProductPricing(@RequestParam Integer fromUnitId, @RequestParam Integer toUnitId, HttpServletRequest request) {
        return unitService.syncUnitProductPricing(fromUnitId, toUnitId, getLoggedInUser(request));
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-unit-brand-mappings", produces = MediaType.APPLICATION_JSON)
    public List<Brand> getUnitBrandMappings(@RequestParam Integer unitId) {
        LOG.info("Request to get Unit Brand Mappings for Unit ID : {}", unitId);
        return unitService.getUnitBrandMappings(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-unit-brand-mappings", produces = MediaType.APPLICATION_JSON)
    public Boolean addUnitBrandMappings(HttpServletRequest request, @RequestParam Integer unitId, @RequestBody List<Brand> brands) {
        LOG.info("Request to add Unit Brand Mappings for Unit ID : {}", unitId);
        return unitService.addUnitBrandMappings(unitId, brands, getLoggedInUser(request));
    }

}
