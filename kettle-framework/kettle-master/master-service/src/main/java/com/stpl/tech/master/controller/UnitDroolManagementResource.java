package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.service.UnitDroolMappingService;
import com.stpl.tech.master.data.model.UnitDroolVersionMapping;
import com.stpl.tech.master.domain.model.DroolVersionDomain;
import com.stpl.tech.master.domain.model.UnitDroolVersionDomain;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;
import static com.stpl.tech.master.service.core.MasterServiceConstants.UNIT_DROOL_RESOURCE_ROOT_CONTEXT;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + UNIT_DROOL_RESOURCE_ROOT_CONTEXT)
@Log4j2
public class UnitDroolManagementResource {

    @Autowired
    private UnitDroolMappingService unitDroolMappingService;

    @RequestMapping(method = RequestMethod.POST, value = "unit-drool-version-mapping", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, Map<String, DroolVersionDomain>> getUnitDroolVersionMapping()
            throws DataNotFoundException {
        log.info("Getting all unit drool version mappings");
        return unitDroolMappingService.getUnitDroolVersionMapping();
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-unit-drool-mapping", produces = MediaType.APPLICATION_JSON)
    public Boolean updateUnitDroolVersionMapping(@RequestParam String droolFile,@RequestParam Integer updatedBy,
                                                 @RequestBody List<UnitDroolVersionDomain> mappingList) throws DataNotFoundException {
        log.info("Getting all unit drool version mappings");
        return unitDroolMappingService.updateUnitDroolVersionMapping(droolFile,updatedBy, mappingList);
    }
}
