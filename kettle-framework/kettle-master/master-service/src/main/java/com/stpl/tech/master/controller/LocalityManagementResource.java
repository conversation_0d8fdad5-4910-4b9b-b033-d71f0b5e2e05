/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.controller;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;
import static com.stpl.tech.master.service.core.MasterServiceConstants.USER_SERVICES_ROOT_CONTEXT;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.MasterCacheManagementService;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + USER_SERVICES_ROOT_CONTEXT)
public class LocalityManagementResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(LocalityManagementResource.class);

	@Autowired
	private MasterCacheManagementService service;

	@RequestMapping(method = RequestMethod.GET, value = "localities")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public View getBudgetSheet() {
		return service.getMasterLocalityView();
	}

	@RequestMapping(method = RequestMethod.POST, value = "upload-localities", consumes = MediaType.MULTIPART_FORM_DATA)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public void uploadDocument(HttpServletRequest request, @RequestParam(value = "file") final MultipartFile file)
			throws IOException {
		LOG.info("Request to upload budgets");
		service.uploadLocalities(file);
	}

}