package com.stpl.tech.master.controller;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.external.excel.GenericExcelManagementService;
import com.stpl.tech.master.data.model.ExcelRequestData;
import com.stpl.tech.master.domain.model.DocUploadTypeDTO;
import com.stpl.tech.master.domain.model.DocumentDetailDTO;
import com.stpl.tech.master.domain.model.FileTypeDTO;
import com.stpl.tech.master.domain.model.MimeType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.GENERIC_EXCEL_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

@Slf4j
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + GENERIC_EXCEL_ROOT_CONTEXT)
public class GenericExcelManagementResource extends AbstractResources {

    @Autowired
    private GenericExcelManagementService genericExcelManagementService;


    @PostMapping(value = "excel/parse", consumes = MediaType.MULTIPART_FORM_DATA)
    public <T> List<T> convertExcelDataToList(
            @RequestParam("file") MultipartFile excelFile,
            @RequestParam String className) throws ClassNotFoundException {
        log.info("Parsing excel data into {}", className);
        return genericExcelManagementService.convertExcelDataIntoList(excelFile, className);
    }

    @PostMapping(value = "excel/generate")
    public View convertListToExcel(@RequestBody ExcelRequestData excelRequestData) throws Exception {
        return genericExcelManagementService.downloadExcelFromRequestData(excelRequestData);
    }

    @Async
    @PostMapping(value = "upload/document-to-s3", consumes = MediaType.MULTIPART_FORM_DATA)
    public CompletableFuture<DocumentDetailDTO> uploadExcelToS3(@RequestParam("file") MultipartFile excelFile,
                                                                @RequestParam String fileName,
                                                                @RequestParam FileTypeDTO type,
                                                                @RequestParam MimeType mimeType,
                                                                @RequestParam DocUploadTypeDTO docType,
                                                                @RequestParam String docName,
                                                                @RequestParam String baseDir,
                                                                HttpServletRequest request) throws DocumentException, IOException {
        return genericExcelManagementService.uploadDocumentAsync(type, mimeType, docType, getLoggedInUser(request), excelFile, fileName, baseDir);
    }

}
