package com.stpl.tech.master.controller;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.FICO_METADATA_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

import java.util.List;

import javax.ws.rs.core.MediaType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.FicoManagementService;
import com.stpl.tech.master.domain.model.FicoDetail;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + FICO_METADATA_ROOT_CONTEXT)
public class FicoManagementResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(FicoManagementResource.class);

	@Autowired
	private FicoManagementService ficoManagementService;

	@RequestMapping(method = RequestMethod.POST, value = "add-fico-details", produces = MediaType.APPLICATION_JSON)
	@ResponseBody
	public boolean addFicoDetails(@RequestBody final FicoDetail ficoDetail) throws DataUpdationException {
		LOG.info("adding fico details");
		return ficoManagementService.addFicoDetails(ficoDetail);
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-fico-details", produces = MediaType.APPLICATION_JSON)
	@ResponseBody
	public List<FicoDetail> getFicodetail() {
		LOG.info("Getting fico details for fico id");
		return ficoManagementService.getFicoList();
	}

	@RequestMapping(method = RequestMethod.GET, value = "update-fico-details", produces = MediaType.APPLICATION_JSON)
	@ResponseBody
	public boolean updateFicoDetails(@RequestBody final FicoDetail ficoDetail)
			throws AuthenticationFailureException, DataNotFoundException {
		LOG.info("Updating fico ", ficoDetail.getFicoDetailId());
		return ficoManagementService.updateFicoDetails(ficoDetail);
	}

}
