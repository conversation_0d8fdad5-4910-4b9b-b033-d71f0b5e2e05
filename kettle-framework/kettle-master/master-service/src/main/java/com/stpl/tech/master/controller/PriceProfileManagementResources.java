package com.stpl.tech.master.controller;


import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.PriceProfileManagementService;
import com.stpl.tech.master.domain.model.PriceProfileDomain;
import com.stpl.tech.master.domain.model.PriceProfileProductMappingDomain;
import com.stpl.tech.master.domain.model.PriceProfileProductMappingRequest;
import com.stpl.tech.master.domain.model.PriceProfileVersionsDomain;
import com.stpl.tech.master.domain.model.ProductPricingMappingBulkUploadResponse;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitPriceProfileMappingBulkUploadResponse;
import com.stpl.tech.master.domain.model.UnitPriceProfileMappingDomain;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.PRICE_PROFILE_METADATA_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

@RestController
@Log4j2
@RequestMapping(value = API_VERSION + SEPARATOR + PRICE_PROFILE_METADATA_ROOT_CONTEXT)
public class PriceProfileManagementResources extends AbstractResources {

    @Autowired
    PriceProfileManagementService priceProfileManagementService;

    @RequestMapping(method = RequestMethod.POST, value = "add-price-profile", produces = MediaType.APPLICATION_JSON,
            consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    PriceProfileDomain addNewPriceProfile(HttpServletRequest request,@RequestBody PriceProfileDomain priceProfile) throws DataUpdationException {
        return priceProfileManagementService.addNewPriceProfile(priceProfile, getLoggedInUser(request));
    }

    @RequestMapping(method = RequestMethod.GET, value = "find-price-profile", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    List<PriceProfileDomain> getAllPriceProfile(HttpServletRequest request, @RequestParam(required = false) String status){
        return priceProfileManagementService.getAllPriceProfilesByStatus(status);
    }

    @RequestMapping(method = RequestMethod.POST, value = "toggle-price-profile-status", produces = MediaType.APPLICATION_JSON,
            consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    Boolean togglePriceProfileStatus(HttpServletRequest request,@RequestBody PriceProfileDomain priceProfile) throws DataUpdationException {
        return priceProfileManagementService.togglePriceProfileStatus(priceProfile, getLoggedInUser(request));
    }

    @RequestMapping(method = RequestMethod.POST, value = "toggle-price-profile-version-status", produces = MediaType.APPLICATION_JSON,
            consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    Boolean togglePriceProfileVersionStatus(HttpServletRequest request,@RequestBody PriceProfileVersionsDomain priceProfileVersion) throws DataUpdationException {
        return priceProfileManagementService.togglePriceProfileVersionStatus(priceProfileVersion, getLoggedInUser(request));
    }

    @RequestMapping(method = RequestMethod.POST, value = "toggle-bulk-price-profile-version-status", produces = MediaType.APPLICATION_JSON,
            consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    Boolean toggleBulkPriceProfileVersionStatus(HttpServletRequest request,@RequestParam String status , @RequestParam Integer versionNo) throws DataUpdationException {
        return priceProfileManagementService.toggleBulkVersionStatus(versionNo,status,getLoggedInUser(request));
    }


    @RequestMapping(method = RequestMethod.POST, value = "create-price-profile-version", produces = MediaType.APPLICATION_JSON,
            consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    PriceProfileDomain createNewProfileVersion(HttpServletRequest request , @RequestParam Integer priceProfileId , @RequestParam Integer clonePriceProfileId
    , @RequestParam Integer clonePriceProfileVersion){
              return priceProfileManagementService.addNewPriceProfileVersion(priceProfileId,clonePriceProfileId,clonePriceProfileVersion,getLoggedInUser(request));
    }

    @RequestMapping(method = RequestMethod.POST, value = "create-price-profile-versions", produces = MediaType.APPLICATION_JSON,
            consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    Boolean createNewProfileVersions(HttpServletRequest request
            , @RequestParam Integer clonePriceProfileVersion){
        return priceProfileManagementService.addNewPriceProfileVersions(clonePriceProfileVersion,getLoggedInUser(request));
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-max-version", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    Integer getMaxVersionNumber(){
        return priceProfileManagementService.getMaxVersion();
    }


    @RequestMapping(method = RequestMethod.POST, value = "get-price-profile-product-mappings", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    List<PriceProfileProductMappingDomain> getPriceProfileProductMapping(@RequestParam(required = false) Integer profileId , @RequestParam(required = false) Integer version,
                                                                         @RequestParam(required = false) Integer productId , @RequestParam(required = false) Integer dimensionCode
          , @RequestBody PriceProfileProductMappingRequest request) throws DataNotFoundException {

       return priceProfileManagementService.getPriceProfileProductMappings(request);

    }

    @RequestMapping(method = RequestMethod.POST, value = "save-price-profile-product-mappings", produces = MediaType.APPLICATION_JSON,
            consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    Boolean savePriceProfileProductMapping(HttpServletRequest httpServletRequest,@RequestBody List<PriceProfileProductMappingDomain> priceProfileProductMappingDomains){

        return  priceProfileManagementService.savePriceProfileProductMappings(priceProfileProductMappingDomains,getLoggedInUser(httpServletRequest));

    }


    @RequestMapping(method = RequestMethod.GET, value = "get-unit-price-profile-mappings", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    List<UnitPriceProfileMappingDomain> getUnitPriceProfileMapping(@RequestParam UnitCategory unitCategory,
                                                                   @RequestParam String unitRegion , @RequestParam Integer partnerId ,
                                                                   @RequestParam Integer brandId , @RequestParam(required = false) Boolean fetchAll){

        return priceProfileManagementService.getUnitPriceProfileMappings(unitCategory,unitRegion,partnerId,brandId,fetchAll);

    }

    @RequestMapping(method = RequestMethod.POST, value = "save-unit-price-profile-mappings", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    Boolean saveUnitPriceProfileMapping(HttpServletRequest httpServletRequest , @RequestBody List<UnitPriceProfileMappingDomain> mappings
     , @RequestParam(required = false) Integer partnerId , @RequestParam(required = false) Integer brandId){
       priceProfileManagementService.saveUnitPriceProfileMappings(mappings,brandId,partnerId , getLoggedInUser(httpServletRequest),
       new AtomicInteger(0) , new AtomicInteger(0));
      return  true;
    }


    @RequestMapping(method = RequestMethod.POST, value = "get-unit-price-profile-sheet")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getUnitPriceProfileSheet(@RequestBody List<UnitPriceProfileMappingDomain> mappings) {
        log.info("Request to download Unit Product Price sheet");
        return priceProfileManagementService.getUnitPriceProfileSheet(mappings);
    }


    @RequestMapping(method = RequestMethod.POST, value = "get-price-profile-product-sheet")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getPriceProfileProductsSheet(@RequestBody List<PriceProfileProductMappingDomain> mappings) {
        log.info("Request to download Product Price Profile sheet");
        return priceProfileManagementService.getProductPriceProfileSheet(mappings);
    }


    @RequestMapping(method = RequestMethod.POST, value = "product/price-profile/bulk-update", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ProductPricingMappingBulkUploadResponse updateBulkProductPriceProfileMappings(HttpServletRequest request,
                                                                                         @RequestParam(value = "file") final MultipartFile file,
                                                                                         Integer updatedBy) throws DataNotFoundException, IOException {
        return priceProfileManagementService.parseAndCreateBulkProductPricingUpdateEvent(file,updatedBy);
    }

   @RequestMapping(method = RequestMethod.POST, value = "unit/price-profile/bulk-update", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitPriceProfileMappingBulkUploadResponse updateBulkUnitPriceProfileMappings(HttpServletRequest request,
                                                                                        @RequestParam(value = "file") final MultipartFile file,
                                                                                        Integer updatedBy) throws DataNotFoundException, IOException {
        return priceProfileManagementService.parseAndCreateUnitPriceProfileMappings(file,updatedBy);
    }







}
