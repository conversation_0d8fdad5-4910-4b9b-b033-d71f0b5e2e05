/**
 * 
 */
package com.stpl.tech.master.monk.helper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeData;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeDetail;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeSummaryData;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeVersionData;

/**
 * <AUTHOR>
 *
 */
public class MonkRecipeVersionHelper {

	public static void process(MonkRecipeVersionData current, MonkRecipeVersionData active,
			List<MonkRecipeDetail> recipes) {
		fillContents(current, recipes);
		MonkRecipeSummaryData summary = new MonkRecipeSummaryData();
		if (active == null) {
			summary.setAdded(current.getContent().size());
			List<String> additions = new ArrayList<>();
			current.getContent().forEach((item) -> {
				additions.add(getAdditionAndRemovals(item));
			});
			summary.setAdditions(additions);
		} else {
			Map<MonkRecipeKey, Pair<MonkRecipeData, MonkRecipeData>> map = new TreeMap<>();
			for (MonkRecipeData d : current.getContent()) {
				MonkRecipeKey key = new MonkRecipeKey(d);
				Pair<MonkRecipeData, MonkRecipeData> p = new Pair<MonkRecipeData, MonkRecipeData>(d, null);
				map.put(key, p);
			}
			for (MonkRecipeData d : active.getContent()) {
				MonkRecipeKey key = new MonkRecipeKey(d);
				if (map.containsKey(key)) {
					map.get(key).setValue(d);
				} else {
					Pair<MonkRecipeData, MonkRecipeData> p = new Pair<MonkRecipeData, MonkRecipeData>(null, d);
					map.put(key, p);
				}
			}
			for (MonkRecipeKey key : map.keySet()) {
				Pair<MonkRecipeData, MonkRecipeData> p = map.get(key);
				if (p.getKey() != null && p.getValue() == null) {
					summary.setAdded(summary.getAdded() + 1);
					summary.getAdditions().add(getAdditionAndRemovals(p.getKey()));
				} else if (p.getKey() == null && p.getValue() != null) {
					summary.setRemoved(summary.getRemoved() + 1);
					summary.getDeletions().add(getAdditionAndRemovals(p.getValue()));
				} else {
					if (!p.getKey().equals(p.getValue())) {
						summary.setUpdated(summary.getUpdated() + 1);
						summary.getUpdations().add(getUpdate(p.getKey(), p.getValue()));
					}
				}
			}
		}
		current.setChangeLog(summary);
	}

	private static String getAdditionAndRemovals(MonkRecipeData d) {
		StringBuffer b = new StringBuffer(d.getProductName());
		b.append(" ");
		b.append(d.getDimension());
		b.append("(");
		b.append(d.getQuantity());
		b.append(") W:");
		b.append(d.getWater());
		b.append(" M:");
		b.append(d.getMilk());
		b.append(" BS:");
		b.append(d.getBoilSettle());
		b.append(" B:");
		b.append(d.getNoOfBoils());
		b.append(" HM:");
		b.append(d.getHeatingTimeMins());
		b.append(" HS:");
		b.append(d.getHeatingTimeSecs());
		return b.toString();
	}

	private static String getUpdate(MonkRecipeData d, MonkRecipeData old) {
		StringBuffer b = new StringBuffer(d.getProductName());
		b.append(" ");
		b.append(d.getDimension());
		b.append("(");
		b.append(d.getQuantity());
		b.append(")");
		if (d.getWater() != old.getWater()) {
			b.append(" W:(");
			b.append(d.getWater() + "," + old.getWater());
			b.append(")");
		}
		if (d.getMilk() != old.getMilk()) {
			b.append(" M:(");
			b.append(d.getMilk() + "," + old.getMilk());
			b.append(")");
		}
		if (d.getBoilSettle() != old.getBoilSettle()) {
			b.append(" BS:(");
			b.append(d.getBoilSettle() + "," + old.getBoilSettle());
			b.append(")");
		}
		if (d.getNoOfBoils() != old.getNoOfBoils()) {
			b.append(" B:(");
			b.append(d.getNoOfBoils() + "," + old.getNoOfBoils());
			b.append(")");
		}
		if (d.getHeatingTimeMins() != old.getHeatingTimeMins()) {
			b.append(" HM:(");
			b.append(d.getHeatingTimeMins() + "," + old.getHeatingTimeMins());
			b.append(")");
		}
		if (d.getHeatingTimeSecs() != old.getHeatingTimeSecs()) {
			b.append(" HS:(");
			b.append(d.getHeatingTimeSecs() + "," + old.getHeatingTimeSecs());
			b.append(")");
		}
		return b.toString();
	}

	public static void fillContents(MonkRecipeVersionData current, List<MonkRecipeDetail> recipes) {

		Map<MonkRecipeKey, MonkRecipeData> map = new TreeMap<>();
		for (MonkRecipeDetail recipe : recipes) {
			for (MonkRecipeData data : recipe.getDatas()) {
				MonkRecipeKey key = new MonkRecipeKey(data);
				map.put(key, data);
			}
		}
		List<MonkRecipeData> contents = new ArrayList<>();
		for (MonkRecipeKey key : map.keySet()) {
			MonkRecipeData data = new MonkRecipeData();
			data.copy(map.get(key));
			contents.add(data);
		}
		current.setContent(contents);
	}
}
