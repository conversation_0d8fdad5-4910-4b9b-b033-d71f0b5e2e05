/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.controller;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.OFFER_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;

import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService;
import com.stpl.tech.master.data.model.SignupOffersCouponDetails;
import com.stpl.tech.master.domain.model.CouponDetailListWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.master.core.CouponMappingType;
import com.stpl.tech.master.core.OfferCategoryType;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CouponMapping;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.service.model.CouponCreateRequest;
import com.stpl.tech.master.service.model.CouponCreateResponse;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + OFFER_MANAGEMENT_ROOT_CONTEXT) // 'v1/offer-management'
public class OfferManagementResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(OfferManagementResource.class);

	@Autowired
	private OfferManagementService offerService;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private OfferManagementExternalService offerManagementExternalService;

	/**
	 * Regular offers shown on POS screen. These are MASS offers.
	 * 
	 * @return
	 * @throws DataNotFoundException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "coupon/offers", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<CouponDetail> getRegularCoupons(HttpServletRequest request) throws DataNotFoundException {
		LOG.info("Request to get MASS offer Coupon Codes");
		Integer loggedInUnit = getLoggedInUnit(request);
		Integer loggedInBrandId = getLoggedInBrandId(request);
		return offerService.getRegularCoupons(loggedInUnit, loggedInBrandId);
	}

	/**
	 * Get All offers in the system
	 * 
	 * @return
	 * @throws DataNotFoundException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "offers", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<OfferDetail> getAllOffers() throws DataNotFoundException {
		LOG.info("Request for getting all offers");
		return offerService.getAllOffers(true);
	}

	@RequestMapping(method = RequestMethod.POST, value = "offers/add", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public OfferDetail addOffer(@RequestBody final OfferDetail offer) throws DataNotFoundException, OfferValidationException {
		LOG.info("Request to Add offer");
		return offerService.addOffer(offer);
	}

	@RequestMapping(method = RequestMethod.POST, value = "offers/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public OfferDetail updateOffer(@RequestBody final OfferDetail offer) throws DataNotFoundException, OfferValidationException {
		LOG.info("Request for updating offer {}", offer.getId());
		return offerService.updateOffer(offer);
	}

	/**
	 * Coupons for a particular offer
	 * 
	 * @param offerId
	 * @return
	 * @throws DataNotFoundException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "offer/coupons", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<CouponDetail> getOfferCoupons(@RequestBody final int offerId,
											  @RequestParam(required = false) boolean applyLimit) throws DataNotFoundException {
		LOG.info("Request for getting all Coupons for offer productId {}", offerId);
		return offerService.getOfferCoupons(offerId, applyLimit);
	}

	@RequestMapping(method = RequestMethod.POST, value = "marketing-partner", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<IdCodeName> getMarketingPartners() throws DataNotFoundException {
		LOG.info("Request for getting all Marketing partners");
		return offerService.getMarketingPartners();
	}

	@RequestMapping(method = RequestMethod.POST, value = "marketing-partner/add", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public IdCodeName addMarketingPartners(@RequestBody final IdCodeName marketingPartner)
			throws DataNotFoundException {
		LOG.info("Request for Adding Marketing Partner with name:", marketingPartner.getName());
		return offerService.addMarketingPartners(marketingPartner);
	}

	@RequestMapping(method = RequestMethod.POST, value = "marketing-partner/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public IdCodeName updateMarketingPartners(@RequestBody final IdCodeName marketingPartner)
			throws DataNotFoundException {
		LOG.info("Request for updating marketing Partner with Id {}", marketingPartner.getId());
		return offerService.updateMarketingPartners(marketingPartner);
	}

	@RequestMapping(method = RequestMethod.POST, value = "marketing-partner/activate", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean activateMarketingPartners(@RequestBody final int marketingPartnerId) throws DataNotFoundException {
		LOG.info("Request for activating marketing Partner with Id {}", marketingPartnerId);
		return offerService.changeMarketingPartnerStatus(marketingPartnerId, AppConstants.ACTIVE);
	}

	@RequestMapping(method = RequestMethod.POST, value = "marketing-partner/deactivate", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean deactivateMarketingPartners(@RequestBody final int marketingPartnerId) throws DataNotFoundException {
		LOG.info("Request for deactivating marketing Partner with Id {}", marketingPartnerId);
		return offerService.changeMarketingPartnerStatus(marketingPartnerId, AppConstants.IN_ACTIVE);
	}

	@RequestMapping(method = RequestMethod.POST, value = "coupon/search", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CouponDetail searchCoupon(@RequestBody final String couponCode,
									 @RequestParam(required = false) boolean applyLimit ) throws DataNotFoundException {
		LOG.info("Request for getting coupon details for code {}", couponCode);
		return offerService.searchCoupon(couponCode, applyLimit);
	}

	@RequestMapping(method = RequestMethod.POST, value = "coupon/add", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CouponDetail addCoupon(@RequestBody final CouponDetail coupon) throws DataNotFoundException {
		LOG.info("Request for Adding Coupon with code: {}", coupon.getCode());
		return offerService.addCoupon(coupon);
	}

	@RequestMapping(method = RequestMethod.POST, value = "coupon/generate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CouponCreateResponse generateCoupon(@RequestBody final CouponCreateRequest coupon)
			throws DataNotFoundException, IOException, DataUpdationException {
		LOG.info("Request for Generating Coupon with code: {}", coupon);
		CouponCreateResponse response = new CouponCreateResponse();
		List<String> couponCodes = offerService.generateUniqueCoupons(coupon.getSampleCode(), coupon.getCouponPrefix(),
				1);
		String generatedCoupon = couponCodes.get(0);
		response.setCouponCode(generatedCoupon);
		CouponDetail detail = offerService.searchCoupon(generatedCoupon, false);
		if (coupon.getContactNumber() != null) {
			CouponMapping mapping = new CouponMapping();
			mapping.setDataType(String.class.getCanonicalName());
			mapping.setGroup(1);
			mapping.setMinValue("1");
			mapping.setStatus("ACTIVE");
			mapping.setType(CouponMappingType.CONTACT_NUMBER.name());
			mapping.setValue(coupon.getContactNumber());
			detail.getCouponMappingList().add(mapping);
		}
		if (coupon.getCompressUrl() != null && coupon.getCompressUrl()) {
			if (coupon.getUrl() != null) {
				String url = String.format(
						coupon.getUrl() + (!coupon.getUrl().contains("?") ? "?couponCode=%s" : "&couponCode=%s"),
						generatedCoupon);
				ShortUrlData urlData = SolsInfiniWebServiceClient.getOTPClient().getShortUrl(url);
				response.setCompressedUrl(urlData.getUrl());
			}
		}

		if (coupon.getStartsFromToday() != null && coupon.getStartsFromToday()) {
			detail.setStartDate(AppUtils.getBusinessDate());
			detail.setEndDate(AppUtils.getDayBeforeOrAfterDay(detail.getStartDate(), coupon.getValidDays()));
		}
		offerService.updateCoupon(detail);
		return response;
	}

	@RequestMapping(method = RequestMethod.POST, value = "coupon/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean updateCoupon(@RequestBody final CouponDetail coupon) throws DataNotFoundException {
		LOG.info("Request for updating coupon with Id {}", coupon.getId());
		offerService.updateCoupon(coupon);
		return true;
	}

	@RequestMapping(method = RequestMethod.POST, value = "coupon/update/all", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean updateCoupons(@RequestBody final List<CouponDetail> coupons) throws DataNotFoundException {
		LOG.info("Request for updating coupons", JSONSerializer.toJSON(coupons));
		return offerService.updateCoupons(coupons);
	}

	@RequestMapping(method = RequestMethod.POST, value = "coupon/update/mappings", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean updateCouponMappings(@RequestBody final List<CouponDetail> coupons) throws DataNotFoundException {
		LOG.info("Request for updating coupons", JSONSerializer.toJSON(coupons));
		return offerService.updateCouponMappings(coupons);
	}

	@RequestMapping(method = RequestMethod.POST, value = "coupon/mapping/activate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean activateCouponMapping(@RequestBody final int couponMappingId) throws DataNotFoundException {
		LOG.info("Request for Activating oupon mapping with Id {}", couponMappingId);
		return offerService.updateCouponMapping(couponMappingId, AppConstants.ACTIVE);
	}

	@RequestMapping(method = RequestMethod.POST, value = "coupon/mapping/deactivate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean deactivateCouponMapping(@RequestBody final int couponMappingId) throws DataNotFoundException {
		LOG.info("Request for De-Activating coupon mapping with Id {}", couponMappingId);
		return offerService.updateCouponMapping(couponMappingId, AppConstants.IN_ACTIVE);
	}

	@RequestMapping(method = RequestMethod.POST, value = "coupon/auto", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<String> generateUniqueCoupons(@RequestParam final String couponCode,
			@RequestParam final String couponPrefix, @RequestParam final int replicateCount)
			throws DataNotFoundException, DataUpdationException {
		LOG.info("Request to replicate coupon code: {} to {} times", couponCode, replicateCount);
		return offerService.generateUniqueCoupons(couponCode, couponPrefix, replicateCount);
	}

	@RequestMapping(method = RequestMethod.POST, value = "coupon/availablity", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean checkCodeAvailiblity(@RequestBody final String couponCode) throws DataNotFoundException {
		LOG.info("Request to Check couponCode Availablity for code {}", couponCode);
		return offerService.checkCodeAvailiblity(couponCode);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment-modes", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Collection<PaymentMode> getAllPaymentModes() throws DataNotFoundException {
		LOG.info("Request to get all Payment Modes");
		return masterDataCache.getAllPaymentMode();
	}

	@RequestMapping(method = RequestMethod.POST, value = "offer-category", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Map<String, List<OfferCategoryType>> getOfferTypes() throws DataNotFoundException {
		return offerService.getOfferCategories();
	}

	@RequestMapping(method = RequestMethod.GET, value = "offerAccountsCategories", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<IdName> offerAccountsCategories() throws DataNotFoundException {
		return offerService.getOfferAccountsCategories();
	}


	@RequestMapping(method = RequestMethod.GET, value = "coupon/customer", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CouponDetailListWrapper getCouponForCustomer( HttpServletRequest request, @RequestParam final String contactNumber,
													   @RequestParam final String date, @RequestParam (required = false) final String brandId)
			throws DataNotFoundException, DataUpdationException {
		LOG.info("Request to get coupon code: {} to {} date", contactNumber, date);
		CouponDetailListWrapper couponDetailListWrapper = new CouponDetailListWrapper();
		if (Objects.isNull(brandId)) {
			Integer brandIdFromHeaders = AbstractResources.getLoggedInBrandId(request);
			couponDetailListWrapper.setList(offerManagementExternalService.getAllCouponForCustomer(contactNumber, date, brandIdFromHeaders));
		} else {
			couponDetailListWrapper.setList(offerManagementExternalService.getAllCouponForCustomer(contactNumber, date, Integer.valueOf(brandId)));
		}
		return couponDetailListWrapper;
	}

	@RequestMapping(method = RequestMethod.POST, value = "coupon/update-customer-coupon", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Boolean updateCustomerCouponData(@RequestParam final String oldContactNumber, @RequestParam final String newContactNumber)
			throws DataNotFoundException, DataUpdationException {
		LOG.info("Request to update coupons for {} with {}", oldContactNumber, newContactNumber);
		return offerManagementExternalService.updateCustomerCouponData(oldContactNumber, newContactNumber);
	}

	@RequestMapping(method = RequestMethod.POST, value = "signup/coupon-detail", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean setSignupCouponMapping(@RequestBody SignupOffersCouponDetails couponDetails) {
		CouponDetail data =offerService.searchCoupon(couponDetails.getCouponCode(), false);
		couponDetails.setOfferDetailId(data.getOffer().getId());
		return offerService.addSignupCouponMapping(couponDetails);
	}

	@RequestMapping(method = RequestMethod.POST, value = "get/loyalty-burn-offers", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Map<String,List<CouponDetail>> getAllLoyaltyBurnOffers(@RequestBody List<String> burnOfferCategoryType,@RequestParam Integer unitId) {
		LOG.info("Getting all Loyalty Burn Offers");
		return offerService.getAllLoyaltyBurnOffers(burnOfferCategoryType,unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "coupon/offers/partner", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CouponDetailListWrapper getCouponForChannelPartner(@RequestParam final String partnerId,
														@RequestParam final String date,
														@RequestParam String offerScope)
			throws DataNotFoundException, DataUpdationException {
		LOG.info("Request to get coupon code: {} to {} date", partnerId, date);
		CouponDetailListWrapper couponDetailListWrapper = new CouponDetailListWrapper();
		couponDetailListWrapper.setList(offerManagementExternalService.getAllCouponForChannelPartner(partnerId, date,offerScope));
		return couponDetailListWrapper;
	}

	@RequestMapping(method = RequestMethod.GET, value = "coupon/offers/unit", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<CouponDetail> getRegularCoupons(@RequestParam Integer unitId, @RequestParam Integer brandId) throws DataNotFoundException {
		LOG.info("Request to get MASS offer Coupon Codes");
		return offerService.getRegularCoupons(unitId, brandId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "get/unit/coupon", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CouponDetail getCouponForUnit(@RequestParam final Integer unitId,
															  @RequestParam final String couponCode)
			throws DataNotFoundException, DataUpdationException {
		LOG.info("Request to get coupon code: {} for unit {} ", couponCode, unitId);
		return offerService.getCouponDetailForUnit(unitId,couponCode);
	}

}
