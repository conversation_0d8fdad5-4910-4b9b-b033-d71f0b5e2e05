package com.stpl.tech.master.controller;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class MasterGarbageCleanExecutor {
    private static final Logger LOG = LoggerFactory.getLogger(MasterGarbageCleanExecutor.class);

    @Scheduled(cron = "0 0 0,4,7,11,15,19 * * *", zone = "GMT+05:30")
    public void garbageCleanExecutor(){
        try {
            LOG.info(" Executing Garbage Clean up");
            System.gc();
        }catch (Exception e){
            LOG.error("Exception Faced While Executing Garbage Clean up");
        }
    }
}
