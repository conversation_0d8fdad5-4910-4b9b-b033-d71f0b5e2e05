package com.stpl.tech.master.data.model;

import com.stpl.tech.master.domain.model.SystemStatus;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * JPA Entity for EMP_ELIGIBILITY_APPROVAL_TYPE table
 */
@Entity
@Table(name = "EMP_ELIGIBILITY_APPROVAL_TYPE")
@Data
public class EmployeeEligibilityApprovalType implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @ManyToOne
    @JoinColumn(name = "EMP_ELIGIBILITY_MAPPING_ID", nullable = false)
    private EmployeeEligibilityMapping empEligibilityMapping;

    @Column(name = "TYPE", length = 100, nullable = false)
    private String type;

    @Enumerated(EnumType.STRING)
    @Column(name = "STATUS", columnDefinition = "ENUM('ACTIVE','IN_ACTIVE')")
    private SystemStatus status;
}
