package com.stpl.tech.master.core.cache;

import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class EmployeeCache {

	@Autowired
	public UserService service;

	private Map<Integer, EmployeeBasicDetail> employees = new HashMap<>();
	private Map<String, EmployeeBasicDetail> employeesByCode = new HashMap<>();

	public EmployeeBasicDetail getEmployee(int employeeId) {
		if (!employees.containsKey(employeeId)) {
			EmployeeBasicDetail detail = service.getEmployeeBasicDetail(employeeId);
			if (detail == null) {
				return null;
			}
			employees.put(employeeId, detail);
		}
		return employees.get(employeeId);
	}



	public void removeEmployee(int employeeId) {
		if (employees.contains<PERSON><PERSON>(employeeId)) {
			employees.remove(employeeId);
		}
	}
}
