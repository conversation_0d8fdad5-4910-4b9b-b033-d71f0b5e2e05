package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.EmployeeEligibilityMappingMetadata;

import java.util.List;

/**
 * DAO interface for Employee Eligibility Mapping Metadata operations
 */
public interface EmployeeEligibilityMappingMetadataDao extends AbstractMasterDao {
    
    /**
     * Find metadata by approval type
     * @param approvalType approval type
     * @return list of metadata
     */
    List<EmployeeEligibilityMappingMetadata> findByApprovalType(String approvalType);
    
    /**
     * Find metadata by status
     * @param status status
     * @return list of metadata
     */
    List<EmployeeEligibilityMappingMetadata> findByStatus(String status);
    
    /**
     * Find metadata by approval type and status
     * @param approvalType approval type
     * @param status status
     * @return list of metadata
     */
    List<EmployeeEligibilityMappingMetadata> findByApprovalTypeAndStatus(String approvalType, String status);
    
    /**
     * Find all active metadata
     * @return list of active metadata
     */
    List<EmployeeEligibilityMappingMetadata> findAllActive();
}
