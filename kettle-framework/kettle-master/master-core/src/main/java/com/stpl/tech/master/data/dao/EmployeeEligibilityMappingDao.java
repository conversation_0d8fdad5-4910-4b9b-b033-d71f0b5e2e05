package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.EmployeeEligibilityMapping;
import com.stpl.tech.master.domain.model.EligibilityType;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingType;
import com.stpl.tech.master.domain.model.SystemStatus;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * DAO interface for Employee Eligibility Mapping operations
 */
public interface EmployeeEligibilityMappingDao extends AbstractMasterDao {
    /**
     * Find mappings by employee ID
     * @param empId employee ID
     * @return list of mappings
     */
    List<EmployeeEligibilityMapping> findByEmpId(String empId);
    List<EmployeeEligibilityMapping> findByEmpIdValueTypeAndStatus(String empId, String value , EmployeeEligibilityMappingType mappingType , EligibilityType eligibilityType , SystemStatus SystemStatus);
    List<EmployeeEligibilityMapping> findByEmpIdValueAndType(String empId, String value, EmployeeEligibilityMappingType mappingType, EligibilityType eligibilityType);

    List<EmployeeEligibilityMapping> findByEmpIdIn(List<Long> empIds);

    // find by mapping id in a map
    List<EmployeeEligibilityMapping> findByMappingIdIn(@NotNull List<Long> mappingIds);
}
