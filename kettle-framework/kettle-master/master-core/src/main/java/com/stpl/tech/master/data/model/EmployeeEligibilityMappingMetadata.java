package com.stpl.tech.master.data.model;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * JPA Entity for EMP_ELIGIBILITY_MAPPING_METADATA table
 */
@Entity
@Table(name = "EMP_ELIGIBILITY_MAPPING_METADATA")
@Data
public class EmployeeEligibilityMappingMetadata implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "APPROVAL_TYPE", length = 45, nullable = false)
    private String approvalType;

    @Column(name = "STATUS", length = 45, nullable = false)
    private String status;
}
