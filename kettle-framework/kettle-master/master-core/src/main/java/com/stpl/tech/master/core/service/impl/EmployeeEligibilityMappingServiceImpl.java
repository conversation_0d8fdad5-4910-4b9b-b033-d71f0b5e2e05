package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.cache.EmployeeCache;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.excel.GenericExcelManagementService;
import com.stpl.tech.master.core.service.EmployeeEligibilityMappingService;
import com.stpl.tech.master.data.dao.EmployeeEligibilityMappingDao;
import com.stpl.tech.master.data.dao.EmployeeEligibilityApprovalTypeDao;
import com.stpl.tech.master.data.model.EmployeeEligibilityMapping;
import com.stpl.tech.master.data.model.EmployeeEligibilityApprovalType;
import com.stpl.tech.master.data.model.ExcelRequestData;
import com.stpl.tech.master.domain.model.BulkEmployeeMappingUploadResponse;
import com.stpl.tech.master.domain.model.EligibilityType;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingColumn;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingRequest;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingResponse;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingType;
import com.stpl.tech.master.domain.model.EmployeeEligibilityApprovalTypeRequest;
import com.stpl.tech.master.domain.model.SystemStatus;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.View;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Service implementation for Employee Eligibility Mapping operations
 */
@Service
@Transactional
public class EmployeeEligibilityMappingServiceImpl implements EmployeeEligibilityMappingService {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeEligibilityMappingServiceImpl.class);

    @Autowired
    private EmployeeEligibilityMappingDao employeeEligibilityMappingDao;

    @Autowired
    private EmployeeEligibilityApprovalTypeDao employeeEligibilityApprovalTypeDao;

    @Autowired
    private GenericExcelManagementService genericExcelManagementService;

    @Autowired
    private EmployeeCache empCache;

    @Autowired
    private MasterDataCache masterDataCache;


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public int saveEmployeeEligibilityMappingList(List<EmployeeEligibilityMappingRequest> requestList, String createdBy, AtomicInteger successCount) throws DataUpdationException {
        if(Objects.isNull(requestList)){
            return -1;
        }
        Map<String,Integer> empIdMap = masterDataCache.getEmployeeBasicDetails().values().stream()
                .filter(emp->emp.getEmployeeCode()!=null)
                .collect(Collectors.toMap(EmployeeBasicDetail::getEmployeeCode, EmployeeBasicDetail::getId));
        empIdMap = empIdMap.entrySet().stream().filter(e->requestList.stream()
                .anyMatch(r->r.getEmployeeCode().equals(e.getKey()))).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        LOG.info("Saving batch employee eligibility mappings, total records: {}", requestList.size());

        List<EmployeeEligibilityMapping> mappingsToInsert = new ArrayList<>();
        Map<String, EmployeeEligibilityMapping> existingMap = buildExistingMappingMap(requestList);
        for (EmployeeEligibilityMappingRequest req : requestList) {
//            EmployeeBasicDetail employeeBasicDetail = empCache.getEmployeeByCode(req.getEmployeeCode());
//            String empId = String.valueOf(employeeBasicDetail.getId());
            String empId = String.valueOf(empIdMap.get(req.getEmployeeCode()));
            try {
                String key = buildKey(empId, req.getValue(), req.getMappingType(), req.getEligibilityType(), req.getStatus());
                if(!existingMap.containsKey(key)) {
                    EmployeeEligibilityMapping mapping = setEmployeeEligibilityMapping(createdBy, req,empId);
                    mappingsToInsert.add(mapping);
                    successCount.getAndIncrement();
                } else {
                    LOG.warn("Skipping duplicate employee eligibility mapping for empId={}, value={}, mappingType={}, eligibilityType={}, status={}",
                            empId, req.getValue(), req.getMappingType(), req.getEligibilityType(), req.getStatus());
                }
            } catch (Exception e) {
                LOG.error("Error processing Employee Eligibility Mapping request for empId={}", empId, e);
            }
        }
        LOG.info("Total Employee Eligibility mappings to insert: {}, for empCode: {} ", mappingsToInsert.size(), requestList.get(0).getEmployeeCode());
        if (!mappingsToInsert.isEmpty()) {
            employeeEligibilityMappingDao.batchInsert(mappingsToInsert);

            List<EmployeeEligibilityMappingResponse> responses = mappingsToInsert.stream()
                    .map(this::convertToResponse)
                    .collect(Collectors.toList());

            LOG.info("Successfully batch inserted {} mappings", mappingsToInsert.size());
            LOG.debug("Converted responses: {}", responses);
        }
        return successCount.get();
    }

    private EmployeeEligibilityMapping setEmployeeEligibilityMapping(String createdBy, EmployeeEligibilityMappingRequest req, String empId) {
        EmployeeEligibilityMapping mapping = new EmployeeEligibilityMapping();
        mapping.setEligibilityType(req.getEligibilityType());
        mapping.setEmpId(empId);
        mapping.setMappingType(req.getMappingType());
        mapping.setValue(req.getValue());
        mapping.setCreatedBy(createdBy);
        mapping.setUpdatedBy(createdBy);
        mapping.setStatus(req.getStatus());
        mapping.setStartDate(req.getStartDate());
        mapping.setEndDate(req.getEndDate());
        mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
        mapping.setUpdatedAt(AppUtils.getCurrentTimestamp());
        
        // Handle approval types if present
        if (req.getApprovalTypes() != null && !req.getApprovalTypes().isEmpty()) {
            for (EmployeeEligibilityApprovalTypeRequest approvalTypeReq : req.getApprovalTypes()) {
                EmployeeEligibilityApprovalType approvalType = new EmployeeEligibilityApprovalType();
                approvalType.setEmpEligibilityMapping(mapping);
                approvalType.setType(approvalTypeReq.getType());
                approvalType.setStatus(approvalTypeReq.getStatus());
                mapping.getApprovalTypes().add(approvalType);
            }
        }
        
        return mapping;
    }

    // update the status for existingMapping
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public BulkEmployeeMappingUploadResponse updateEmployeeEligibilityMapping(List<EmployeeEligibilityMappingRequest> requestList, String createdBy) {
        Map<Long, EmployeeEligibilityMapping> mappingMap = new HashMap<>();
        for( EmployeeEligibilityMapping mapping : getEligibilityEligibilityMappingsById(requestList.stream().map(EmployeeEligibilityMappingRequest::getId).collect(Collectors.toList()))){
            mappingMap.put(mapping.getId(), mapping);
        }
        BulkEmployeeMappingUploadResponse response = new BulkEmployeeMappingUploadResponse();
        response.setTotalRecords(requestList.size());
        response.setValidationErrors(new ArrayList<>());

        for(EmployeeEligibilityMappingRequest req : requestList){
            try{
                EmployeeEligibilityMapping mapping = mappingMap.get(req.getId());
                mapping.setStatus(req.getStatus());
                mapping.setStartDate(req.getStartDate());
                mapping.setEndDate(req.getEndDate());
                mapping.setUpdatedBy(createdBy);
                mapping.setUpdatedAt(AppUtils.getCurrentTimestamp());
                employeeEligibilityMappingDao.update(mapping);
                response.setSuccessfullyProcessed(response.getSuccessfullyProcessed() + 1);
                response.setSuccess(true);
                response.setMessage("All mappings saved successfully!");
                response.setValidRecords(response.getValidRecords() + 1);
                response.setInvalidRecords(response.getInvalidRecords() - 1);
                LOG.info("Updated employee eligibility mapping for empCode={}, value={}, mappingType={}, eligibilityType={}, status={}, startDate={}, endDate={}",
                        req.getEmployeeCode(), req.getValue(), req.getMappingType(), req.getEligibilityType(), req.getStatus(), req.getStartDate(), req.getEndDate());
            } catch (Exception e) {
                LOG.error("Error processing employee eligibility mapping request for empCode={}", req.getEmployeeCode(), e);
            }
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public BulkEmployeeMappingUploadResponse saveEmployeeEligibilityBatchMappings(List<EmployeeEligibilityMappingRequest> requestList, String createdBy) throws DataUpdationException {
        LOG.info("Saving employee attendance batch mappings from UI, total records: {}", requestList.size());

        BulkEmployeeMappingUploadResponse response = new BulkEmployeeMappingUploadResponse();
        response.setTotalRecords(requestList.size());
        response.setValidationErrors(new ArrayList<>());
        AtomicInteger successCount = new AtomicInteger(0);

        try {
            int successMappingCount = saveEmployeeEligibilityMappingList(requestList, createdBy , successCount);
            response.setSuccessfullyProcessed(successMappingCount);
            response.setValidRecords(successMappingCount);
            response.setInvalidRecords(requestList.size() - successMappingCount);
            response.setSuccess(successMappingCount > 0);

            if (successMappingCount == requestList.size()) {
                response.setMessage("All mappings saved successfully!");
            } else if (successMappingCount > 0) {
                response.setMessage(String.format("Partially saved: %d successful, %d failed.", successMappingCount, requestList.size() - successMappingCount));
            } else {
                response.setMessage("Failed to save any employee eligibility mappings due to duplicate entries.");
                response.setSuccess(false);
            }

            LOG.info("Employee Eligibility batch save completed: {} successful out of {} total", successMappingCount, requestList.size());

        } catch (Exception e) {
            LOG.error("Error in batch save operation", e);
            response.setSuccess(false);
            response.setMessage("Error occurred during batch save: " + e.getMessage());
            response.setSuccessfullyProcessed(0);
            response.setValidRecords(0);
            response.setInvalidRecords(requestList.size());
            throw new DataUpdationException("Error in batch save operation: " + e.getMessage());
        }

        return response;
    }

    @Override
    @Transactional( rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED )
    public List<EmployeeEligibilityMappingResponse> getEligibilityEligibilityMappingsByEmpId(String empId) {
        try {
            LOG.info("Getting mappings for empId: {}", empId);
            List<EmployeeEligibilityMapping> mappings = employeeEligibilityMappingDao.findByEmpId(empId);
            return mappings.stream()
                    .map(this::convertToResponse)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOG.error("Error getting employee eligibility mappings for empId: {}", empId, e);
            throw e;
        }
    }

    @Override
    @Transactional( rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED )
    public View prepareEmployeeMappingExcel(List<Long> empIds) throws Exception {
        try {
            List<EmployeeEligibilityMapping> mappings = employeeEligibilityMappingDao.findByEmpIdIn(empIds);

            List<String> headers = Arrays.asList(
                    EmployeeEligibilityMappingColumn.EMPLOYEE_ID.getColumnName(),
                    EmployeeEligibilityMappingColumn.EMPLOYEE_NAME.getColumnName(),
                    EmployeeEligibilityMappingColumn.EMPLOYEE_CODE.getColumnName(),
                    EmployeeEligibilityMappingColumn.MAPPING_TYPE.getColumnName(),
                    EmployeeEligibilityMappingColumn.VALUE.getColumnName(),
                    EmployeeEligibilityMappingColumn.UNIT_NAME.getColumnName(),
                    EmployeeEligibilityMappingColumn.ELIGIBILITY_TYPE.getColumnName(),
                    EmployeeEligibilityMappingColumn.STATUS.getColumnName(),
                    EmployeeEligibilityMappingColumn.START_DATE.getColumnName(),
                    EmployeeEligibilityMappingColumn.END_DATE.getColumnName()
            );

            List<Object[]> rows = new ArrayList<>();

            for (EmployeeEligibilityMapping mapping : mappings) {
                Long empId = Long.valueOf(mapping.getEmpId());
                EmployeeBasicDetail employeeDetail = masterDataCache.getEmployeeBasicDetail(empId.intValue());
                Map<Integer, Unit> units = masterDataCache.getUnits();

                String empName = employeeDetail != null ? employeeDetail.getName() : "";
                String empCode = employeeDetail != null ? employeeDetail.getEmployeeCode() : "";

                // Get unit name based on mapping type
                String unitName = "";
                if ("UNIT".equals(mapping.getMappingType().name())) {
                    try {
                        Integer unitId = Integer.valueOf(mapping.getValue());
                        Unit unit = units.get(unitId);
                        unitName = unit != null ? unit.getName() : "";
                    } catch (NumberFormatException e) {
                        LOG.warn("Invalid unit ID format: {}", mapping.getValue());
                        unitName = "";
                    }
                } else {
                    // For CITY and REGION mappings, the value is empty
                    unitName = "";
                }

                rows.add(new Object[]{
                        empId,
                        empName,
                        empCode,
                        mapping.getMappingType(),
                        mapping.getValue(),
                        unitName,
                        mapping.getEligibilityType(),
                        mapping.getStatus(),
                        mapping.getStartDate(),
                        mapping.getEndDate()
                });
            }

            ExcelRequestData excelRequestData = new ExcelRequestData();
            excelRequestData.setFileName("Employee_Mappings");
            excelRequestData.setHeaderNames(headers);
            excelRequestData.setBody(rows);

            return genericExcelManagementService.downloadExcelFromRequestData(excelRequestData);
        }
        catch (Exception e) {
            LOG.error("Error while preparing employee eligibility mapping excel", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View downloadEmployeeEligibilityMappingBulkUploadTemplate() throws Exception {
        try {
            List<String> headers = Arrays.asList(
                    EmployeeEligibilityMappingColumn.ELIGIBILITY_TYPE.getColumnName(),
                    EmployeeEligibilityMappingColumn.EMPLOYEE_CODE.getColumnName(),
                    EmployeeEligibilityMappingColumn.MAPPING_TYPE.getColumnName(),
                    EmployeeEligibilityMappingColumn.STATUS.getColumnName(),
                    EmployeeEligibilityMappingColumn.VALUE.getColumnName(),
                    EmployeeEligibilityMappingColumn.START_DATE.getColumnName(),
                    EmployeeEligibilityMappingColumn.END_DATE.getColumnName()
            );

            List<String[]> rows = Arrays.asList(
                    new String[]{"ATTENDANCE", "EMP001", "UNIT", "ACTIVE", "100000", "2024-01-01", "2024-12-31"},
                    new String[]{"APPROVAL", "EMP002", "CITY", "ACTIVE", "Delhi", "2024-01-01", ""},
                    new String[]{"ATTENDANCE", "EMP003", "REGION", "ACTIVE", "North", "", "2024-12-31"}
            );

            List<Object[]> objectRows = rows.stream()
                    .map(arr -> Arrays.copyOf(arr, arr.length, Object[].class))
                    .collect(Collectors.toList());

            ExcelRequestData excelRequestData = new ExcelRequestData();
            excelRequestData.setFileName("Employee_Eligibility_Mapping_Template");
            excelRequestData.setHeaderNames(headers);
            excelRequestData.setBody(objectRows);

            return genericExcelManagementService.downloadExcelFromRequestData(excelRequestData);
        }
        catch (Exception e) {
            LOG.error("Error while downloading bulk upload template for Employee Attendance Mapping", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BulkEmployeeMappingUploadResponse validateEmpMappingBulkUpload(List<EmployeeEligibilityMappingRequest> bulkData) throws Exception {
        LOG.info("Validating bulk upload with {} records", bulkData.size());
        try {
            BulkEmployeeMappingUploadResponse response = new BulkEmployeeMappingUploadResponse();
            response.setTotalRecords(bulkData.size());
            response.setValidationErrors(new ArrayList<>());

            List<EmployeeEligibilityMappingRequest> validRecords = new ArrayList<>();
            int rowNumber = 2; // Starting from row 2 (after header)

            // Validate each record
            Set<String> seenKeys = new HashSet<>();
            for (EmployeeEligibilityMappingRequest request : bulkData) {
                List<BulkEmployeeMappingUploadResponse.ExistingIds> list = response.getExistingIds();
                if(list == null){
                    list = new ArrayList<>();
                }
                int errorCountBefore = response.getValidationErrors().size();
                Long mappingId = validateEmpMappingBulkUploadRecord(request, rowNumber, seenKeys, response);
                int errorCountAfter = response.getValidationErrors().size();
                boolean hasValidationError = errorCountAfter > errorCountBefore;

                if (Objects.nonNull(mappingId) && !hasValidationError) {
                    // Set the ID on the request object for update logic
                    request.setId(mappingId);
                    validRecords.add(request);
                    list.add(new BulkEmployeeMappingUploadResponse.ExistingIds(rowNumber, request.getEmployeeCode(), mappingId));
                    response.setExistingIds(list);
                } else if (mappingId == null && !hasValidationError) {
                    // No existing mapping found and no validation errors - this is a new record
                    validRecords.add(request);
                }
                rowNumber++;
            }

            response.setValidRecords(validRecords.size());
            response.setInvalidRecords(bulkData.size() - validRecords.size());
            response.setSuccessfullyProcessed(0); // No processing done yet
            response.setSuccess(response.getValidationErrors().isEmpty());
            response.setMessage(String.format("Validation completed: %d valid records, %d invalid records found.",
                    validRecords.size(), response.getValidationErrors().size()));

            LOG.info("Bulk validation completed: {} valid, {} invalid", validRecords.size(), response.getValidationErrors().size());
            return response;
        }
        catch (Exception e) {
            LOG.error("Error while validating bulk upload for Employee Eligibility Mapping", e);
            throw e;
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public BulkEmployeeMappingUploadResponse processEmpMappingBulkUpload(List<EmployeeEligibilityMappingRequest> bulkData, String createdBy) throws Exception {
        LOG.info("Processing bulk upload with {} records", bulkData.size());

        try {
            // loop over bulkData.
            /*
            if id != null it means update call updateEmployeeEligibilityMapping
            else insert call saveEmployeeEligibilityMappingList .
             */
            AtomicInteger successCount = new AtomicInteger(0);
            int successEligibilityCount = 0;
            List<EmployeeEligibilityMappingRequest> updateRequests = new ArrayList<>();
            List<EmployeeEligibilityMappingRequest> insertRequests = new ArrayList<>();
            for (EmployeeEligibilityMappingRequest request : bulkData) {
                if (request.getId() != null) {
                    updateRequests.add(request);
                } else {
                    insertRequests.add(request);
                }
            }
            if (!updateRequests.isEmpty()) {
                updateEmployeeEligibilityMapping(updateRequests, createdBy);
            }
            if (!insertRequests.isEmpty()) {
                successEligibilityCount = saveEmployeeEligibilityMappingList(insertRequests, createdBy, successCount);
            }
            BulkEmployeeMappingUploadResponse response = new BulkEmployeeMappingUploadResponse();
            response.setTotalRecords(bulkData.size());
            response.setSuccessfullyProcessed(successEligibilityCount);
            response.setSuccess(successEligibilityCount > 0);
            response.setMessage(String.format("Processed %d valid records successfully.", updateRequests.size() + insertRequests.size()));
            LOG.info("Bulk upload completed for Employee Eligibility Mapping: {} successful", updateRequests.size() + insertRequests.size());
            return response;
        }
        catch (Exception e) {
            LOG.error("Error while processing bulk upload for Employee Eligibility Mapping", e);
            throw e;
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    private Long validateEmpMappingBulkUploadRecord(EmployeeEligibilityMappingRequest request, int rowNumber, Set<String> seenKeys, BulkEmployeeMappingUploadResponse response) {
        Map<String,Integer> empIdMap = masterDataCache.getEmployeeBasicDetails().values().stream()
                .filter(emp->emp.getEmployeeCode()!=null)
                .collect(Collectors.toMap(EmployeeBasicDetail::getEmployeeCode, EmployeeBasicDetail::getId));
        empIdMap = empIdMap.entrySet().stream().filter(e->request.getEmployeeCode()
                .equals(e.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        try {
            // Check for duplicate rows
            if (isDuplicateRow(request, seenKeys)) {
                response.getValidationErrors().add(createValidationError(rowNumber, request.getEmployeeCode(), "value", "Duplicate row in upload file", request.getValue()));
                return null;
            }

            // Validate eligibility type
            if (request.getEligibilityType() == null) {
                response.getValidationErrors().add(createValidationError(rowNumber, request.getEmployeeCode(), "eligibilityType", "Eligibility type is required", ""));
            }

            // Validate employee ID
            if (request.getEmployeeCode() == null || request.getEmployeeCode().trim().isEmpty()) {
                response.getValidationErrors().add(createValidationError(rowNumber, request.getEmployeeCode(), "employeeCode", "Employee Code is required", request.getEmployeeCode()));
            }

            // Validate mapping type
            if (request.getMappingType() == null) {
                response.getValidationErrors().add(createValidationError(rowNumber, request.getEmployeeCode(), "mappingType", "Mapping type is required", ""));
            }

            // Validate value
            if (request.getValue() == null || request.getValue().trim().isEmpty()) {
                response.getValidationErrors().add(createValidationError(rowNumber, request.getEmployeeCode(), "value", "Value is required", request.getValue()));
            }

            // validate status
            if (request.getStatus() == null) {
                response.getValidationErrors().add(createValidationError(rowNumber, request.getEmployeeCode(), "status", "Status is required", ""));
            }

            // validate dates
            if (request.getStartDate() != null && request.getEndDate() != null) {
                if (request.getEndDate().before(request.getStartDate())) {
                    response.getValidationErrors().add(createValidationError(rowNumber, request.getEmployeeCode(), "endDate", "End date must be after start date", request.getEndDate().toString()));
                }
            }
            Integer empId = empIdMap.get(request.getEmployeeCode());
            // validation for duplicate mapping
            EmployeeEligibilityMapping existingMapping = employeeEligibilityMappingDao.findByEmpIdValueAndType(String.valueOf(empId),request.getValue(),request.getMappingType(),request.getEligibilityType()).stream().findFirst().orElse(null);
            if (existingMapping != null) {
                if (existingMapping.getStatus().equals(request.getStatus())) {
                    // Same status - this is a true duplicate, add error
                    response.getValidationErrors().add(createValidationError(rowNumber, request.getEmployeeCode(), "value", "Mapping already exists with same status", request.getValue()));
                    return null;
                } else {
                    // Different status - this should be an update, return the existing ID
                    return existingMapping.getId();
                }
            }
            // No existing mapping found - this will be a new insert
            return null;
        }
        catch (Exception e) {
            LOG.error("Error while validating bulk upload record for Employee Eligibility Mapping", e);
            throw e;
        }
    }
    private boolean isDuplicateRow(EmployeeEligibilityMappingRequest request, Set<String> seenKeys) {
        try {
            String key = String.format("%s|%s|%s|%s|%s",
                    request.getEmployeeCode(),
                    request.getValue(),
                    request.getStatus() != null ? request.getStatus().name() : "",
                    request.getMappingType() != null ? request.getMappingType().name() : "",
                    request.getEligibilityType() != null ? request.getEligibilityType().name() : "");
            if (!seenKeys.contains(key)) {
                seenKeys.add(key);
                return false;
            }

            return seenKeys.contains(key);
        }
        catch (Exception e) {
            LOG.error("Error while checking for duplicate row in bulk upload for Employee Eligibility Mapping", e);
            throw e;
        }
    }

    private BulkEmployeeMappingUploadResponse.BulkUploadValidationError createValidationError(int rowNumber, String empId, String field, String message, String originalValue) {
        try {
            BulkEmployeeMappingUploadResponse.BulkUploadValidationError error = new BulkEmployeeMappingUploadResponse.BulkUploadValidationError();
            error.setRowNumber(rowNumber);
            error.setEmpId(empId);
            error.setField(field);
            error.setErrorMessage(message);
            error.setOriginalValue(originalValue);
            return error;
        }
        catch (Exception e) {
            LOG.error("Error while creating validation error for Employee Eligibility Mapping", e);
            throw e;
        }
    }

    /**
     * Convert entity to response DTO
     * @param mapping entity
     * @return response DTO
     */
    private EmployeeEligibilityMappingResponse convertToResponse(EmployeeEligibilityMapping mapping) {
        EmployeeEligibilityMappingResponse response = new EmployeeEligibilityMappingResponse();
        response.setId(mapping.getId());
        response.setCreatedAt(mapping.getCreatedAt());
        response.setCreatedBy(mapping.getCreatedBy());
        response.setEligibilityType(mapping.getEligibilityType());
        response.setEmpId(mapping.getEmpId());
        response.setMappingType(mapping.getMappingType());
        response.setStatus(mapping.getStatus());
        response.setUpdatedAt(mapping.getUpdatedAt());
        response.setUpdatedBy(mapping.getUpdatedBy());
        response.setValue(mapping.getValue());
        response.setStartDate(mapping.getStartDate());
        response.setEndDate(mapping.getEndDate());
        // Convert approval types to simple string array
        if (mapping.getApprovalTypes() != null && !mapping.getApprovalTypes().isEmpty()) {
            List<String> approvalTypeStrings = mapping.getApprovalTypes().stream()
                    .map(EmployeeEligibilityApprovalType::getType)
                    .collect(Collectors.toList());
            response.setApprovalTypes(approvalTypeStrings);
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<String> getAvailableApprovalTypes() {
        try {
           
        } catch (Exception e) {
            LOG.error("Error getting available approval types", e);
            throw e;
        }
    }

    @Override
    public String buildKey(String empId, String value, EmployeeEligibilityMappingType mappingType, EligibilityType eligibilityType, SystemStatus status) {
        try {
            return empId + "|" + value + "|" + mappingType + "|" + eligibilityType + "|" + status;
        } catch (Exception e) {
            LOG.error("Error while building key for Employee Eligibility Mapping", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String, EmployeeEligibilityMapping> buildExistingMappingMap(
            List<EmployeeEligibilityMappingRequest> requestList) {
        try {
            Map<String, Integer> empIdMap = masterDataCache.getEmployeeBasicDetails().values().stream()
                    .filter(emp->emp.getEmployeeCode()!=null)
                    .collect(Collectors.toMap(EmployeeBasicDetail::getEmployeeCode, EmployeeBasicDetail::getId));
            List<Long> empIds = requestList.stream()
                    .map(r -> {
                        EmployeeBasicDetail employeeBasicDetail = masterDataCache.getEmployeeBasicDetail(empIdMap.get(r.getEmployeeCode()));
                        return Long.valueOf(employeeBasicDetail.getId());
                    })
                    .distinct()
                    .collect(Collectors.toList());

            List<EmployeeEligibilityMapping> existingMappings = employeeEligibilityMappingDao.findByEmpIdIn(empIds);

            Map<String, EmployeeEligibilityMapping> existingMap = new HashMap<>();

            for (EmployeeEligibilityMapping mapping : existingMappings) {
                String key = buildKey(
                        mapping.getEmpId(),
                        mapping.getValue(),
                        mapping.getMappingType(),
                        mapping.getEligibilityType(),
                        mapping.getStatus());
                existingMap.put(key, mapping);
            }
            return existingMap;
        }
        catch (Exception e) {
            LOG.error("Error while building existing mapping map for Employee Eligibility Mapping", e);
            throw e;
        }
    }
    // checking for id in request list from dao
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<EmployeeEligibilityMapping> getEligibilityEligibilityMappingsById(@NotNull(message = " Id is required ") List<Long> mappingIds) {
        try {
            LOG.info("Getting mappings for mappingIds: {}", mappingIds);
            Map<Long, EmployeeEligibilityMapping> mappingMap = new HashMap<>();
            for( EmployeeEligibilityMapping mapping : employeeEligibilityMappingDao.findByMappingIdIn(mappingIds)){
                mappingMap.put(mapping.getId(), mapping);
            }
            return new ArrayList<>(mappingMap.values());
        } catch (Exception e) {
            LOG.error("Error getting mappings for mappingIds: {}", mappingIds, e);
            throw e;
        }
    }
}
