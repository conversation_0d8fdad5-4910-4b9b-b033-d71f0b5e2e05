/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao.impl;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;

import com.stpl.tech.master.data.dao.AbstractMasterDao;

public class AbstractMasterDaoImpl implements AbstractMasterDao {

    private static final Logger ALOG = LoggerFactory.getLogger(AbstractMasterDaoImpl.class);

    @PersistenceContext(unitName = "MasterDataSourcePUName")
    @Qualifier(value = "MasterDataSourceEMFactory")
    protected EntityManager manager;

    @Override
    public <T> T update(T data) {

        try {
            data = manager.merge(data);
            manager.flush();
            return data;
        } catch (Exception e) {
            ALOG.error("Error updating {}", data.getClass().getName(), e);
        }
        return null;
    }

    @Override
    public <T> T add(T data) {
        try {
            manager.persist(data);
            manager.flush();
            return data;
        } catch (Exception e) {
            ALOG.error("Error adding {}", data.getClass().getName(), e);
        }
        return null;
    }

    @Override
    public <T> List<T> addAll(List<T> list) {
        List<T> l = new ArrayList<>();
        try {
            for (T data : list) {
                manager.persist(data);
                l.add(data);
            }
            manager.flush();
            return l;
        } catch (Exception e) {
            ALOG.error("Error adding {}", list.getClass().getName(), e);
        }
        return new ArrayList<>();
    }

    @Override
    public <T> List<T> findAll(Class<T> data) {
        Query query = manager.createQuery("FROM " + data.getName() + " T");
        return query.getResultList();
    }

    @Override
    public <T, R> T find(Class<T> data, R key) {
        return manager.find(data, key);
    }

    @Override
    public <T> void delete(T data) {
        try {
            manager.remove(data);
            manager.flush();
        } catch (Exception e) {
            ALOG.error("Error deleting {}", data.getClass().getName(), e);
        }
    }

}
