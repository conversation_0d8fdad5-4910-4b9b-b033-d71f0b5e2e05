package com.stpl.tech.master.data.dao.impl;

import com.stpl.tech.master.data.dao.EmployeeEligibilityMappingMetadataDao;
import com.stpl.tech.master.data.model.EmployeeEligibilityMappingMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;

/**
 * DAO implementation for Employee Eligibility Mapping Metadata operations
 */
@Repository
public class EmployeeEligibilityMappingMetadataDaoImpl extends AbstractMasterDaoImpl implements EmployeeEligibilityMappingMetadataDao {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeEligibilityMappingMetadataDaoImpl.class);

    @Override
    public List<EmployeeEligibilityMappingMetadata> findByApprovalType(String approvalType) {
        try {
            Query query = manager.createQuery(
                    "FROM EmployeeEligibilityMappingMetadata em WHERE em.approvalType = :approvalType");
            query.setParameter("approvalType", approvalType);
            return query.getResultList();
        } catch (NoResultException e) {
            LOG.error("Cannot find metadata by approvalType: {}", approvalType);
            return new ArrayList<>();
        } catch (Exception e) {
            LOG.error("Error finding metadata by approvalType: {}", approvalType, e);
            throw e;
        }
    }

    @Override
    public List<EmployeeEligibilityMappingMetadata> findByStatus(String status) {
        try {
            Query query = manager.createQuery(
                    "FROM EmployeeEligibilityMappingMetadata em WHERE em.status = :status");
            query.setParameter("status", status);
            return query.getResultList();
        } catch (NoResultException e) {
            LOG.error("Cannot find metadata by status: {}", status);
            return new ArrayList<>();
        } catch (Exception e) {
            LOG.error("Error finding metadata by status: {}", status, e);
            throw e;
        }
    }

    @Override
    public List<EmployeeEligibilityMappingMetadata> findByApprovalTypeAndStatus(String approvalType, String status) {
        try {
            Query query = manager.createQuery(
                    "FROM EmployeeEligibilityMappingMetadata em WHERE em.approvalType = :approvalType AND em.status = :status");
            query.setParameter("approvalType", approvalType);
            query.setParameter("status", status);
            return query.getResultList();
        } catch (NoResultException e) {
            LOG.error("Cannot find metadata by approvalType: {} and status: {}", approvalType, status);
            return new ArrayList<>();
        } catch (Exception e) {
            LOG.error("Error finding metadata by approvalType: {} and status: {}", approvalType, status, e);
            throw e;
        }
    }

    @Override
    public List<EmployeeEligibilityMappingMetadata> findAllActive() {
        try {
            Query query = manager.createQuery(
                    "FROM EmployeeEligibilityMappingMetadata em WHERE em.status = 'ACTIVE'");
            return query.getResultList();
        } catch (NoResultException e) {
            LOG.error("Cannot find active metadata");
            return new ArrayList<>();
        } catch (Exception e) {
            LOG.error("Error finding active metadata", e);
            throw e;
        }
    }
}
