package com.stpl.tech.master.data.dao.impl;

import com.stpl.tech.master.data.dao.EmployeeEligibilityApprovalTypeDao;
import com.stpl.tech.master.data.model.EmployeeEligibilityApprovalType;
import com.stpl.tech.master.data.model.EmployeeEligibilityMapping;
import com.stpl.tech.master.domain.model.EligibilityType;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.List;

/**
 * DAO implementation for Employee Eligibility Approval Type operations
 */
@Repository
public class EmployeeEligibilityApprovalTypeDaoImpl implements EmployeeEligibilityApprovalTypeDao {


}
