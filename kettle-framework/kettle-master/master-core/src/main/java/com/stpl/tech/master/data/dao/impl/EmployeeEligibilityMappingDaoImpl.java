package com.stpl.tech.master.data.dao.impl;

import com.stpl.tech.master.data.dao.EmployeeEligibilityMappingDao;
import com.stpl.tech.master.data.model.EmployeeEligibilityApprovalType;
import com.stpl.tech.master.data.model.EmployeeEligibilityMapping;
import com.stpl.tech.master.domain.model.EligibilityType;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingType;
import com.stpl.tech.master.domain.model.SystemStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DAO implementation for Employee Eligibility Mapping operations
 */
@Repository
public class EmployeeEligibilityMappingDaoImpl extends AbstractMasterDaoImpl implements EmployeeEligibilityMappingDao {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeEligibilityMappingDaoImpl.class);


    @Override
    public List<EmployeeEligibilityMapping> findByEmpId(String empId) {
        try {
            Query query = manager.createQuery(
                    "SELECT DISTINCT ea FROM EmployeeEligibilityMapping ea " +
                    "LEFT JOIN FETCH ea.approvalTypes at " +
                    "WHERE ea.empId = :empId " +
                    "AND (at IS NULL OR at.status = 'ACTIVE')");
            query.setParameter("empId", empId);
            return query.getResultList();
        } catch (NoResultException e){
            LOG.error("Cannot find mappings by empId: {}" , empId);
            return new ArrayList<>();
        } catch (Exception e) {
            LOG.error("Error finding mappings by empId: {}", empId, e);
            throw e;
        }
    }

    @Override
    public List<EmployeeEligibilityMapping> findByEmpIdValueTypeAndStatus(String empId, String value , EmployeeEligibilityMappingType mappingType , EligibilityType eligibilityType , SystemStatus SystemStatus) {
        try {
            Query query = manager.createQuery(
                    "FROM EmployeeEligibilityMapping ea WHERE ea.empId = :empId AND ea.value = :value AND ea.mappingType = :mappingType AND ea.eligibilityType = :eligibilityType AND ea.status = :status");
            query.setParameter("empId", empId);
            query.setParameter("value", value);
            query.setParameter("mappingType", mappingType);
            query.setParameter("eligibilityType", eligibilityType);
            query.setParameter("status", SystemStatus);
            return query.getResultList();
        }catch (NoResultException e){
            LOG.error("Cannot find mappings by empId: {} , value: {} and mappingType: {} and eligibilityType: {} and status: {}" , empId, value, mappingType,eligibilityType, SystemStatus);
            return new ArrayList<>();
        } catch (Exception e) {
            LOG.error("Error finding mappings by empId: {} , value: {} and mappingType: {} and eligibilityType: {} and status: {}" , empId, value, mappingType,eligibilityType, SystemStatus, e);
            throw e;
        }
    }

    // find by emp_id, value mapping type and value
    @Override
    public List<EmployeeEligibilityMapping> findByEmpIdValueAndType(String empId, String value, EmployeeEligibilityMappingType mappingType, EligibilityType eligibilityType) {
        try {
            Query query = manager.createQuery(
                    "FROM EmployeeEligibilityMapping ea WHERE ea.empId = :empId AND ea.value = :value AND ea.mappingType = :mappingType AND ea.eligibilityType = :eligibilityType");
            query.setParameter("empId", empId);
            query.setParameter("value", value);
            query.setParameter("mappingType", mappingType);
            query.setParameter("eligibilityType", eligibilityType);
            return query.getResultList();
        } catch (NoResultException e){
            LOG.error("Cannot find mappings by empId: {} , value: {} and mappingType: {} and eligibilityType: {}" , empId, value, mappingType,eligibilityType);
            return new ArrayList<>();
        } catch (Exception e) {
            LOG.error("Error finding mappings by empId: {} , value: {} and mappingType: {} and eligibilityType: {}" , empId, value, mappingType,eligibilityType, e);
            throw e;
        }
    }

    @Override
    public List<EmployeeEligibilityMapping> findByEmpIdIn(List<Long> empIds) {
        try {
            List<String> empIdStrings = empIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());
            Query query = manager.createQuery(
                    "FROM EmployeeEligibilityMapping ea WHERE ea.empId IN :empIds");
            query.setParameter("empIds", empIdStrings);
            return query.getResultList();
        } catch (NoResultException e){
            LOG.error("Cannot find mappings by empIds: {}" , empIds);
            return new ArrayList<>();
        } catch (Exception e) {
            LOG.error("Error finding mappings by empIds: {}", empIds, e);
            throw e;
        }
    }


    // find by mapping id in a map
    @Override
    public List<EmployeeEligibilityMapping> findByMappingIdIn(@NotNull List<Long> mappingIds) {
        try {
            Query query = manager.createQuery(
                    "FROM EmployeeEligibilityMapping ea WHERE ea.id IN :mappingIds");
            query.setParameter("mappingIds", mappingIds);
            return query.getResultList();
        } catch (NoResultException e){
            LOG.error("Cannot find mappings by mappingIds: {}" , mappingIds);
            return new ArrayList<>();
        } catch (Exception e) {
            LOG.error("Error finding mappings by mappingIds: {}", mappingIds, e);
            throw e;
        }
    }

}
