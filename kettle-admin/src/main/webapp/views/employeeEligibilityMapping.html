
<link rel="stylesheet" href="css/employee_eligibility_mapping.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<div class="app-container" data-ng-init="init()" ng-controller="employeeEligibilityMappingCtrl">
    <div class="container">
        <div class="header">
            <h1>Employee Eligibility Mapping</h1>
            <p>Configure attendance and approval eligibility mappings for employees</p>
        </div>

        <div class="step-indicator">
            <div class="step active" id="step1">
                <div class="step-number">1</div>
                <div class="step-label">Select Employees</div>
            </div>
            <div class="step-connector"></div>
            <div class="step" id="step2">
                <div class="step-number">2</div>
                <div class="step-label">Configure Mappings</div>
            </div>
            <div class="step-connector"></div>
            <div class="step" id="step3">
                <div class="step-number">3</div>
                <div class="step-label">Review & Save</div>
            </div>
        </div>

        <div class="main-content">
            <!-- Step 1: Employee Selection -->
            <div class="employee-selection" id="employeeSelection" ng-show="currentStep === 1">
                <div class="search-section">
                    <div class="search-title">🔍 Filter Employees</div>
                    <div class="filters-row">
                        <div class="form-group">
                            <label>Department</label>
                            <div class="searchable-dropdown-container">
                                <input type="text" class="searchable-input" placeholder="Search departments..."
                                       ng-model="departmentSearch" ng-focus="showDepartmentDropdown = true"
                                       ng-blur="hideDepartmentDropdown()" ng-keyup="filterDepartments()">
                                <div class="dropdown-options" ng-class="{show: showDepartmentDropdown}">
                                    <div class="dropdown-option" ng-click="selectDepartment({name: 'All Departments'})"
                                         ng-mousedown="$event.preventDefault()">
                                        All Departments
                                    </div>
                                    <div class="dropdown-option" ng-repeat="dept in filteredDepartments"
                                         ng-click="selectDepartment(dept)" ng-mousedown="$event.preventDefault()">
                                        {{dept.name || dept.departmentName || dept}}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Designation</label>
                            <div class="searchable-dropdown-container">
                                <input type="text" class="searchable-input" placeholder="Search designations..."
                                       ng-model="designationSearch" ng-focus="showDesignationDropdown = true"
                                       ng-blur="hideDesignationDropdown()" ng-keyup="filterDesignations()">
                                <div class="dropdown-options" ng-class="{show: showDesignationDropdown}">
                                    <div class="dropdown-option" ng-click="selectDesignation({name: 'All Designations'})"
                                         ng-mousedown="$event.preventDefault()">
                                        All Designations
                                    </div>
                                    <div class="dropdown-option" ng-repeat="designation in filteredDesignations"
                                         ng-click="selectDesignation(designation)" ng-mousedown="$event.preventDefault()">
                                        {{designation.name || designation.designationName || designation}}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="search-btn" ng-click="searchEmployees()">🔍 Search</button>
                        <button class="bulk-btn" ng-click="exportAllEmployeeMappings()" style="width: 200px; height: 50px; margin:auto;">📊 Export Current</button>
                        <div class="form-group">


                        </div>
                    </div>
                </div>

                <div class="employee-list">
                    <div class="list-header">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <div class="list-title">👥 Employee List</div>
                            <input type="text" class="form-control" placeholder="Search..." ng-model="searchText" ng-change="searchEmployees()" style="width: 200px; margin: 0;">
                        </div>
                        <div class="select-all" ng-click="toggleSelectAll()">
                            <input type="checkbox" ng-checked="selectedEmployees.length === filteredEmployees.length && filteredEmployees.length > 0"> Select All ({{filteredEmployees.length}} employees)
                        </div>
                    </div>

                    <!-- Employee Count Info -->
                    <div class="employee-count-info">
                        <span class="count-badge">{{filteredEmployees.length}}</span> Employees Found
                        <span ng-show="filteredEmployees.length !== employees.length"> (Filtered from {{employees.length}} total)</span>
                    </div>

                    <div class="employee-item" ng-repeat="employee in paginatedEmployees">
                        <input type="checkbox" class="employee-checkbox" ng-checked="isEmployeeSelected(employee)" ng-click="toggleEmployeeSelection(employee)">
                        <div class="employee-info">
                            <div class="employee-name">{{employee.name || employee.firstName + ' ' + employee.lastName}}</div>
                            <div class="employee-details">Employee Code: {{employee.employeeCode}} • Employee ID: {{ employee.id }}• {{employee.designation || employee.designationName || 'N/A'}} • {{employee.department || employee.departmentName || 'N/A'}}</div>
                        </div>
                        <div class="employee-actions">
                            <p>See Mappings : </p>
                            <i class="fa fa-eye view-mapping-icon" ng-click="viewEmployeeMappings(employee)" title="View Mappings" style="cursor: pointer; color: #3498db; font-size: 16px;"></i>
                        </div>

                    </div>

                    <div ng-if="filteredEmployees.length === 0" class="empty-state">
                        <div class="empty-icon">👥</div>
                        <div class="empty-text">No employees found</div>
                        <div class="empty-subtext">Try adjusting your search or filter criteria</div>
                    </div>

                    <!-- Pagination -->
                    <div class="pagination-container" ng-show="filteredEmployees.length > 0">
                        <div class="pagination-info">
                            Showing {{getStartIndex()}} to {{getEndIndex()}} of {{filteredEmployees.length}} employees
                        </div>
                        <div class="pagination-controls">
                            <button class="pagination-btn" ng-click="goToPage(1)" ng-disabled="currentPage === 1">«</button>
                            <button class="pagination-btn" ng-click="goToPage(currentPage - 1)" ng-disabled="currentPage === 1">‹</button>

                            <button class="pagination-btn" ng-repeat="page in getVisiblePages()"
                                    ng-click="goToPage(page)" ng-class="{active: page === currentPage}"
                                    ng-show="page !== '...'">
                                {{page}}
                            </button>
                            <span class="pagination-ellipsis" ng-repeat="page in getVisiblePages()" ng-show="page === '...'">...</span>

                            <button class="pagination-btn" ng-click="goToPage(currentPage + 1)" ng-disabled="currentPage === totalPages">›</button>
                            <button class="pagination-btn" ng-click="goToPage(totalPages)" ng-disabled="currentPage === totalPages">»</button>
                        </div>
                    </div>
                </div>

                <div class="selected-summary">
                    <div class="employees-chips">
                        <span ng-repeat="employee in selectedEmployees | limitTo: 14" class="employee-chip">
                            {{employee.name || employee.firstName + ' ' + employee.lastName}} ({{employee.employeeId || employee.id}})
                        </span>
                        <span ng-if="selectedEmployees.length > 14" class="employee-chip">
                            ... and {{selectedEmployees.length - 14}} more
                        </span>
                        <div ng-if="selectedEmployees.length === 0" class="empty-chips">
                            No employees selected
                        </div>
                    </div>
                    <!-- <button class=" next-btn" ng-click=" "> Clone </button>-->
                    <button class="next-btn" ng-click="goToMapping()">Next Step →</button>
                </div>
                <div class="bulk-actions">
                    <input type="file" id="bulkUploadFile" accept=".xlsx,.xls" style="display: none;" file-upload="handleBulkUpload($event)">
                    <button class="bulk-btn" ng-click="triggerBulkUpload()">📤 Bulk Upload</button>
                    <button class="bulk-btn" ng-click="downloadTemplate()">📥 Download Template</button>
                </div>
            </div>

            <!-- Step 2: Mapping Configuration -->
            <div class="mapping-section" id="mappingSection" ng-show="currentStep === 2">
                <div class="eligibility-tabs">
                    <button class="tab-btn active" ng-click="switchEligibilityTab('attendance')">
                        📅 Attendance Eligibility
                    </button>
                    <button class="tab-btn" ng-click="switchEligibilityTab('approval')">
                        ✅ Approval Eligibility
                    </button>
                </div>

                <div class="eligibility-content" id="attendanceTab">
                    <div class="mapping-tabs">
                        <button class="mapping-tab"
                                ng-class="{active: selectedMappingType === 'unit'}"
                                ng-click="switchMappingTab('unit')">Unit Mapping</button>

                        <button class="mapping-tab"
                                ng-class="{disabled: !isMultiMappingAllowed()}"
                                ng-click="isMultiMappingAllowed() && switchMappingTab('city')">City Mapping</button>

                        <button class="mapping-tab"
                                ng-class="{disabled: !isMultiMappingAllowed()}"
                                ng-click="isMultiMappingAllowed() && switchMappingTab('region')">Region Mapping</button>
                    </div>

                    <div class="mapping-form">
                        <div class="search-title">🏢 Configure Unit Mapping</div>

                        <!-- Unit Filters Section -->
                        <div class="unit-filters-section" ng-show="selectedMappingType === 'unit'">
                            <div class="unit-filters-title">
                                🔍 Filter Units
                                <button class="clear-filters-btn" ng-click="clearUnitFilters()" title="Clear all filters">Clear Filters</button>
                            </div>
                            <div class="filter-row">
                                <div class="form-group">
                                    <label>Category</label>
                                    <div class="searchable-dropdown-container">
                                        <input type="text" class="searchable-input" placeholder="Search categories..."
                                               ng-model="categorySearch" ng-focus="showCategoryDropdown = true"
                                               ng-blur="hideCategoryDropdown()" ng-keyup="filterCategories()">
                                        <div class="dropdown-options" ng-class="{show: showCategoryDropdown}">
                                            <div class="dropdown-option" ng-repeat="category in filteredCategories"
                                                 ng-click="selectCategory(category)" ng-mousedown="$event.preventDefault()">
                                                {{category.name}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>City</label>
                                    <div class="searchable-dropdown-container">
                                        <input type="text" class="searchable-input" placeholder="Search cities..."
                                               ng-model="citySearch" ng-focus="showCityDropdown = true"
                                               ng-blur="hideCityDropdown()" ng-keyup="filterCities()">
                                        <div class="dropdown-options" ng-class="{show: showCityDropdown}">
                                            <div class="dropdown-option" ng-repeat="city in filteredCities"
                                                 ng-click="selectCity(city)" ng-mousedown="$event.preventDefault()">
                                                {{city.name || city}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Region</label>
                                    <div class="searchable-dropdown-container">
                                        <input type="text" class="searchable-input" placeholder="Search regions..."
                                               ng-model="regionSearch" ng-focus="showRegionDropdown = true"
                                               ng-blur="hideRegionDropdown()" ng-keyup="filterRegions()">
                                        <div class="dropdown-options" ng-class="{show: showRegionDropdown}">
                                            <div class="dropdown-option" ng-repeat="region in filteredRegions"
                                                 ng-click="selectRegion(region)" ng-mousedown="$event.preventDefault()">
                                                {{region.name || region}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Unit Zones</label>
                                    <div class="searchable-dropdown-container">
                                        <input type="text" class="searchable-input" placeholder="Search Unit Zones..."
                                               ng-model="unitZoneSearch" ng-focus="showUnitZoneDropdown = true"
                                               ng-blur="hideUnitZoneDropdown()" ng-keyup="filterUnitZones()">
                                        <div class="dropdown-options" ng-class="{show: showUnitZoneDropdown}">
                                            <div class="dropdown-option" ng-repeat="unitZone in filteredUnitZones"
                                                 ng-click="selectUnitZone(unitZone)" ng-mousedown="$event.preventDefault()">
                                                {{unitZone.name}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="filter-info" ng-show="filteredUnits.length !== allUnits.length">
                                📊 Showing {{filteredUnits.length}} of {{allUnits.length}} units based on your filters
                            </div>
                        </div>

                        <!-- Unit Multi-Selection Row -->
                        <div class="unit-selection-row" ng-show="selectedMappingType === 'unit'">
                            <div class="form-group">
                                <div class="searchable-dropdown-container">
                                    <input type="text" class="searchable-input" placeholder="Search units..."
                                           ng-model="unitSearch" ng-focus="showUnitDropdown = true"
                                           ng-blur="hideUnitDropdown()" ng-keyup="filterUnitsDropdown()">
                                    <div class="dropdown-options multi-select-dropdown" ng-class="{show: showUnitDropdown}">
                                        <div class="dropdown-option multi-select-option" ng-repeat="unit in filteredUnitsDropdown"
                                             ng-mousedown="$event.preventDefault()">
                                            <input type="checkbox" ng-checked="isUnitSelected(unit)"
                                                   ng-click="toggleUnitSelection(unit)" class="multi-select-checkbox">
                                            <span class="multi-select-label">{{unit.id}} - {{unit.name}}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="selected-items-display" ng-show="selectedUnits.length > 0">
                                    <div class="selected-items-header">Selected Units ({{selectedUnits.length}}):</div>
                                    <div class="selected-items-list">
                                        <span class="selected-item-tag" ng-repeat="unit in selectedUnits">
                                            {{unit.name}}
                                            <button class="remove-selected-item" ng-click="toggleUnitSelection(unit)">×</button>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- City Multi-Selection -->
                        <div class="form-row" ng-show="selectedMappingType === 'city'">
                            <div class="form-group">
                                <div class="searchable-dropdown-container">
                                    <input type="text" class="searchable-input" placeholder="Search cities..."
                                           ng-model="cityMappingSearch" ng-focus="showCityMappingDropdown = true"
                                           ng-blur="hideCityMappingDropdown()" ng-keyup="filterCitiesMapping()">
                                    <div class="dropdown-options multi-select-dropdown" ng-class="{show: showCityMappingDropdown}">
                                        <div class="dropdown-option multi-select-option" ng-repeat="city in filteredCitiesMapping"
                                             ng-mousedown="$event.preventDefault()">
                                            <input type="checkbox" ng-checked="isCitySelected(city)"
                                                   ng-click="toggleCitySelection(city)" class="multi-select-checkbox">
                                            <span class="multi-select-label">{{city.cityName || city.name}}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="selected-items-display" ng-show="selectedCities.length > 0">
                                    <div class="selected-items-header">Selected Cities ({{selectedCities.length}}):</div>
                                    <div class="selected-items-list">
                                        <span class="selected-item-tag" ng-repeat="city in selectedCities">
                                            {{city.cityName || city.name}}
                                            <button class="remove-selected-item" ng-click="toggleCitySelection(city)">×</button>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Region Multi-Selection -->
                        <div class="form-row" ng-show="selectedMappingType === 'region'">
                            <div class="form-group">
                                <div class="searchable-dropdown-container">
                                    <input type="text" class="searchable-input" placeholder="Search regions..."
                                           ng-model="regionMappingSearch" ng-focus="showRegionMappingDropdown = true; console.log('Region dropdown opened, filteredRegionsMapping:', filteredRegionsMapping)"
                                           ng-blur="hideRegionMappingDropdown()" ng-keyup="filterRegionsMapping()">
                                    <div class="dropdown-options multi-select-dropdown" ng-class="{show: showRegionMappingDropdown}">
                                        <div class="dropdown-option multi-select-option" ng-repeat="region in filteredRegionsMapping"
                                             ng-mousedown="$event.preventDefault()">
                                            <input type="checkbox" ng-checked="isRegionSelected(region)"
                                                   ng-click="toggleRegionSelection(region)" class="multi-select-checkbox">
                                            <span class="multi-select-label">{{region.regionName || region.name || region}}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="selected-items-display" ng-show="selectedRegions.length > 0">
                                    <div class="selected-items-header">Selected Regions ({{selectedRegions.length}}):</div>
                                    <div class="selected-items-list">
                                        <span class="selected-item-tag" ng-repeat="region in selectedRegions">
                                            {{region.regionName || region.name || region}}
                                            <button class="remove-selected-item" ng-click="toggleRegionSelection(region)">×</button>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Date Range Section - Only for Attendance Eligibility -->
                        <div class="form-row" ng-show="eligibilityType === 'attendance'">
                            <div class="form-group">
                                <label>Start Date</label>
                                <input type="date" class="form-control" ng-model="startDate" placeholder="Select start date">
                            </div>
                            <div class="form-group">
                                <label>End Date</label>
                                <input type="date" class="form-control" ng-model="endDate" placeholder="Select end date">
                            </div>
                        </div>

                        <!-- Approval Types Multi-Selection - Only for Approval Eligibility -->
                        <div class="form-row" ng-show="eligibilityType === 'approval'">
                            <div class="form-group">
                                <label>Approval Types</label>
                                <div class="searchable-dropdown-container">
                                    <input type="text" class="searchable-input" placeholder="Search approval types..."
                                           ng-model="approvalTypeSearch" ng-focus="showApprovalTypeDropdown = true"
                                           ng-blur="hideApprovalTypeDropdown()" ng-keyup="filterApprovalTypes()">
                                    <div class="dropdown-options multi-select-dropdown" ng-class="{show: showApprovalTypeDropdown}">
                                        <div class="dropdown-option multi-select-option" ng-repeat="approvalType in filteredApprovalTypes"
                                             ng-mousedown="$event.preventDefault()">
                                            <input type="checkbox" ng-checked="isApprovalTypeSelected(approvalType)"
                                                   ng-click="toggleApprovalTypeSelection(approvalType)" class="multi-select-checkbox">
                                            <span class="multi-select-label">{{approvalType}}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="selected-items-display" ng-show="selectedApprovalTypes.length > 0">
                                    <div class="selected-items-header">Selected Approval Types ({{selectedApprovalTypes.length}}):</div>
                                    <div class="selected-items-list">
                                        <span class="selected-item-tag" ng-repeat="approvalType in selectedApprovalTypes">
                                            {{approvalType}}
                                            <button class="remove-selected-item" ng-click="toggleApprovalTypeSelection(approvalType)">×</button>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Apply To Section -->
                        <div class="form-row">
                            <div class="form-group">
                                <label>Apply To</label>
                                <select class="form-control" ng-model="applyTo" id="applyToSelect" ng-change="toggleIndividualSelection()">
                                    <option value="all">All Selected Employees</option>
                                    <option value="individual">Individual Selection</option>
                                </select>
                            </div>
                        </div>

                        <!-- Individual Selection Panel -->
                        <div class="individual-selection-panel" id="individualSelectionPanel" ng-show="applyTo === 'individual'">
                            <div class="search-title">👤 Select Individual Employees</div>
                            <div class="individual-employees">
                                <div ng-repeat="employee in selectedEmployees" class="individual-employee-item">
                                    <input type="checkbox" ng-model="employee.selectedForMapping">
                                    <span>{{employee.name || employee.firstName + ' ' + employee.lastName}} ({{employee.employeeId || employee.id}})</span>
                                </div>
                            </div>
                        </div>

                        <button class="add-mapping-btn" ng-click="addMapping()">➕ Add Mapping</button>
                    </div>

                    <div class="current-mappings">
                        <div class="mappings-header">
                            <div class="search-title">📋 To Be Mapped</div>
                            <div class="mappings-controls">
                                <div class="mappings-count">{{mappings.length}} mappings</div>
                                <div class="mappings-actions">
                                    <input type="text" class="mapping-search" placeholder="🔍 Search mappings..." id="mappingSearch" ng-keyup="filterMappings()">
                                    <select class="mapping-filter" ng-model="mappingFilter" id="mappingFilter" ng-change="filterMappings()">
                                        <option value="all">All Types</option>
                                        <option value="unit">Unit Only</option>
                                        <option value="city">City Only</option>
                                        <option value="region">Region Only</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Tabular Mapping Display -->
                        <div class="mappings-table-container">
                            <table class="mappings-table" ng-show="tabularMappings.length > 0">
                                <thead>
                                    <tr>
                                        <th>Employee Name</th>
                                        <th>Mapping Type</th>
                                        <th>Mapping Names</th>
                                        <th ng-show="eligibilityType === 'attendance'">Date Range</th>
                                        <th ng-show="eligibilityType === 'approval'">Approval Types</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="tabMapping in tabularMappings" class="mapping-table-row">
                                        <td class="employee-name-cell">
                                            {{tabMapping.empName}}
                                        </td>
                                        <td class="mapping-type-cell">
                                            <span class="mapping-type-badge" ng-class="{'unit-badge': tabMapping.mappingType === 'UNIT', 'city-badge': tabMapping.mappingType === 'CITY', 'region-badge': tabMapping.mappingType === 'REGION'}">
                                                {{tabMapping.mappingType}}
                                            </span>
                                        </td>
                                        <td class="mapping-names-cell">
                                            {{tabMapping.mappingNames}}
                                        </td>
                                        <td class="mapping-dates-cell" ng-show="eligibilityType === 'attendance'">
                                            <span class="date-range-display">{{formatDateRange(tabMapping.startDate, tabMapping.endDate)}}</span>
                                        </td>
                                        <td class="approval-types-cell" ng-show="eligibilityType === 'approval'">
                                            {{tabMapping.approvalTypes || 'N/A'}}
                                        </td>
                                        <td class="mapping-actions-cell">
                                            <button class="remove-mapping-btn" ng-click="removeEmployeeMapping(tabMapping)" title="Remove Mapping">
                                                🗑️
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="no-mappings" ng-show="tabularMappings.length === 0">
                            <div class="empty-state">
                                <div class="empty-icon">📝</div>
                                <div class="empty-text">No mappings </div>
                                <div class="empty-subtext">Add some mappings to get started</div>
                            </div>
                        </div>
                    </div>
                </div>


            <!-- Step 3: Review & Save -->
            <div class="review-section" id="reviewSection" ng-show="currentStep === 3">
                <div class="review-header">
                    <div class="search-title">📋 Review & Confirm Mappings</div>
                    <p class="review-subtitle">Please review the mappings below and confirm to save them.</p>
                </div>

                <div class="review-summary">
                    <div class="summary-card">
                        <div class="summary-header">
                            <h3>📊 Summary</h3>
                        </div>
                        <div class="summary-stats">
                            <div class="stat-item">
                                <div class="stat-number">{{selectedEmployees.length}}</div>
                                <div class="stat-label">Selected Employees</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">{{tabularMappings.length}}</div>
                                <div class="stat-label">Total Mappings</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">{{eligibilityType}}</div>
                                <div class="stat-label">Eligibility Type</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="review-mappings">
                    <div class="mappings-header">
                        <div class="search-title">🔗 Mapping Details</div>
                    </div>

                    <!-- Tabular Review Display -->
                    <div class="review-table-container">
                        <table class="review-table" ng-show="tabularMappings.length > 0">
                            <thead>
                                <tr>
                                    <th>Employee Name</th>
                                    <th>Mapping Type</th>
                                    <th>Mapping Names</th>
                                    <th ng-show="eligibilityType === 'attendance'">Date Range</th>
                                    <th ng-show="eligibilityType === 'approval'">Approval Types</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="tabMapping in tabularMappings" class="review-table-row">
                                    <td class="review-employee-cell">
                                        {{tabMapping.empName}}
                                    </td>
                                    <td class="review-type-cell">
                                        <span class="mapping-type-badge" ng-class="{'unit-badge': tabMapping.mappingType === 'UNIT', 'city-badge': tabMapping.mappingType === 'CITY', 'region-badge': tabMapping.mappingType === 'REGION'}">
                                            {{tabMapping.mappingType}}
                                        </span>
                                    </td>
                                    <td class="review-names-cell">
                                        {{tabMapping.mappingNames}}
                                    </td>
                                    <td class="review-dates-cell" ng-show="eligibilityType === 'attendance'">
                                        <span class="date-range-display">{{formatDateRange(tabMapping.startDate, tabMapping.endDate)}}</span>
                                    </td>
                                    <td class="review-approval-types-cell" ng-show="eligibilityType === 'approval'">
                                        {{tabMapping.approvalTypes || 'N/A'}}
                                    </td>
                                    <td class="review-status-cell">
                                        <span class="status-ready">✓ Ready to Save</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="no-mappings" ng-show="tabularMappings.length === 0">
                        <div class="empty-state">
                            <div class="empty-icon">📝</div>
                            <div class="empty-text">No mappings to review</div>
                            <div class="empty-subtext">Go back to add some mappings</div>
                        </div>
                    </div>
                </div>

                <div class="review-actions">
                    <button class="back-btn" ng-click="goBackToMapping()">← Back to Mappings</button>
                    <button class="confirm-btn" ng-click="confirmMappings()" ng-disabled="tabularMappings.length === 0">✅ Confirm & Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Employee Mapping View Modal -->
    <div class="modal-overlay" ng-show="showEmployeeMappingModal" ng-click="closeEmployeeMappingModal()">
        <div class="modal-content employee-mapping-modal" ng-click="$event.stopPropagation()">
            <div class="modal-header">
                <h3>👥 Employee Mappings - {{selectedEmployeeForView.name || selectedEmployeeForView.firstName + ' ' + selectedEmployeeForView.lastName}}</h3>
                <button class="modal-close" ng-click="closeEmployeeMappingModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div ng-show="loadingEmployeeMappings" class="loading-state">
                    <div class="spinner"></div>
                    <p>Loading mappings...</p>
                </div>

                <!-- Confirmation View -->
                <div ng-show="!loadingEmployeeMappings && showConfirmationView" class="confirmation-view">
                    <div class="confirmation-header">
                        <h4>📋 Confirm Changes</h4>
                        <p>Please review the changes you are about to make:</p>
                    </div>

                    <div class="confirmation-summary">
                        <div class="summary-stats">
                            <div class="stat-item">
                                <div class="stat-number">{{modifiedMappings.length}}</div>
                                <div class="stat-label">Total Changes</div>
                            </div>
                        </div>
                    </div>

                    <div class="confirmation-table-container">
                        <table class="confirmation-table">
                            <thead>
                                <tr>
                                    <th>Mapping Type</th>
                                    <th>Eligibility</th>
                                    <th>Value</th>
                                    <th>New Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="mapping in modifiedMappings" class="confirmation-row">
                                    <td>
                                        <span class="mapping-type-badge" ng-class="{'unit-badge': mapping.mappingType === 'UNIT', 'city-badge': mapping.mappingType === 'CITY', 'region-badge': mapping.mappingType === 'REGION'}">
                                            {{mapping.mappingType}}
                                        </span>
                                    </td>
                                    <td class="eligibility-cell">{{mapping.eligibilityType}}</td>
                                    <td class="value-cell">{{mapping.valueName || mapping.value}}</td>
                                    <td class="status-cell">
                                        <span class="status-badge" ng-class="{'active-badge': mapping.status === 'ACTIVE', 'inactive-badge': mapping.status !== 'ACTIVE'}">
                                            {{mapping.status === 'ACTIVE' ? '✓ ACTIVE' : '✗ INACTIVE'}}
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Main View -->
                <div ng-show="!loadingEmployeeMappings && !showConfirmationView">
                    <!-- Search functionality for mappings -->
                    <div ng-show="employeeMappings.length > 0" class="mapping-search-section">
                        <input type="text" class="form-control mapping-search-input"
                               placeholder="🔍 Search mappings by type, value, or status..."
                               ng-model="employeeMappingSearch"
                               ng-change="filterEmployeeMappings()">
                    </div>
                    <div ng-show="filteredEmployeeMappings.length > 0" class="mappings-container-compact">
                        <div class="compact-mapping-row" ng-repeat="mapping in filteredEmployeeMappings">
                            <span class="compact-badge" ng-class="{'unit-badge': mapping.mappingType === 'UNIT', 'city-badge': mapping.mappingType === 'CITY', 'region-badge': mapping.mappingType === 'REGION'}">
                                {{mapping.mappingType}}
                            </span>
                            <span class="compact-eligibility">{{mapping.eligibilityType}}</span>
                            <span class="compact-value">{{mapping.displayValue}}</span>

                            <!-- Status Toggle Buttons -->
                            <div class="status-toggle-container">
                                <button class="status-toggle-btn"
                                        ng-class="{'active': mapping.status === 'ACTIVE', 'inactive': mapping.status !== 'ACTIVE'}"
                                        ng-click="toggleMappingStatus(mapping)"
                                        ng-disabled="savingMappingChanges">
                                    <span ng-if="mapping.status === 'ACTIVE'">✓ ACTIVE</span>
                                    <span ng-if="mapping.status !== 'ACTIVE'">✗ INACTIVE</span>
                                </button>
                            </div>

                            <!-- Editable Date Fields -->
                            <div class="date-edit-container">
                                <div class="date-field-group">
                                    <label class="date-label">Start Date</label>
                                    <input type="date" class="date-input" ng-model="mapping.displayStartDate" 
                                           ng-change="updateMappingDate(mapping, 'startDate')"
                                           ng-disabled="savingMappingChanges">
                                </div>
                                <div class="date-field-group">
                                    <label class="date-label">End Date</label>
                                    <input type="date" class="date-input" ng-model="mapping.displayEndDate" 
                                           ng-change="updateMappingDate(mapping, 'endDate')"
                                           ng-disabled="savingMappingChanges">
                                </div>
                            </div>

                            <span class="compact-creator">by {{mapping.createdBy}}</span>
                        </div>
                    </div>
                    <div ng-show="employeeMappings.length === 0" class="empty-mappings">
                        <div class="empty-icon">📋</div>
                        <div class="empty-text">No mappings found</div>
                        <div class="empty-subtext">This employee has no eligibility mappings configured.</div>
                    </div>
                    <div ng-show="employeeMappings.length > 0 && filteredEmployeeMappings.length === 0" class="empty-mappings">
                        <div class="empty-icon">🔍</div>
                        <div class="empty-text">No mappings match your search</div>
                        <div class="empty-subtext">Try adjusting your search criteria</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-left">
                    <span ng-show="hasUnsavedChanges && !showConfirmationView" class="unsaved-changes-indicator">
                        ⚠️ {{modifiedMappings.length}} unsaved changes
                    </span>
                    <span ng-show="showConfirmationView" class="confirmation-indicator">
                        📋 Reviewing {{modifiedMappings.length}} changes
                    </span>
                </div>
                <div class="modal-footer-right">
                    <!-- Main View Buttons -->
                    <div ng-show="!showConfirmationView">
                        <button class="btn btn-secondary" ng-click="closeEmployeeMappingModal()">Close</button>
                        <button class="btn btn-primary"
                                ng-click="saveMappingStatusChanges()"
                                ng-disabled="!hasUnsavedChanges || savingMappingChanges"
                                ng-show="employeeMappings.length > 0">
                            <span ng-if="!savingMappingChanges">💾 Save Changes</span>
                            <span ng-if="savingMappingChanges">🔄 Saving...</span>
                        </button>
                    </div>

                    <!-- Confirmation View Buttons -->
                    <div ng-show="showConfirmationView">
                        <button class="btn btn-secondary" ng-click="cancelConfirmation()">← Back</button>
                        <button class="btn btn-success"
                                ng-click="confirmAndSave()"
                                ng-disabled="savingMappingChanges">
                            <span ng-if="!savingMappingChanges">✓ Confirm & Save</span>
                            <span ng-if="savingMappingChanges">🔄 Saving...</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Upload Preview Modal -->
    <div class="modal-overlay" ng-show="showBulkUploadModal" ng-click="closeBulkUploadModal()">
        <div class="modal-content bulk-upload-modal" ng-click="$event.stopPropagation()">
            <div class="modal-header">
                <h3>📤 Bulk Upload Preview</h3>
                <button class="modal-close" ng-click="closeBulkUploadModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div ng-show="processingBulkUpload" class="loading-state">
                    <div class="spinner"></div>
                    <p>Processing file...</p>
                </div>
                <div ng-show="!processingBulkUpload">
                    <div ng-show="bulkUploadData.length > 0">
                        <div class="upload-summary">
                            <div class="summary-stats">
                                <div class="stat-item">
                                    <div class="stat-number">{{bulkUploadData.length}}</div>
                                    <div class="stat-label">Total Records</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">{{getValidRecordsCount()}}</div>
                                    <div class="stat-label">Valid Records</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">{{getInvalidRecordsCount()}}</div>
                                    <div class="stat-label">Invalid Records</div>
                                </div>
                            </div>
                        </div>

                        <div class="upload-data-preview">
                            <div class="preview-header">
                                <h4>📋 Data Preview</h4>
                                <div class="preview-filters">
                                    <select ng-model="bulkUploadFilter" ng-change="filterBulkUploadData()">
                                        <option value="all">All Records</option>
                                        <option value="valid">Valid Records Only</option>
                                        <option value="invalid">Invalid Records Only</option>
                                    </select>
                                </div>
                            </div>

                            <div class="preview-table-container">
                                <table class="preview-table">
                                    <thead>
                                        <tr>
                                            <th>Row</th>
                                            <th>Eligibility Type</th>
                                            <th>Employee Code</th>
                                            <th>Mapping Type</th>
                                            <th>Status</th>
                                            <th>Value</th>
                                            <th>Start Date</th>
                                            <th>End Date</th>
                                            <th>Validation</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="record in filteredBulkUploadData"
                                            ng-class="{'invalid-row': !record.isValid, 'valid-row': record.isValid}">
                                            <td>{{record.rowNumber}}</td>
                                            <td>{{record.eligibilityType}}</td>
                                            <td>{{record.empCode}}</td>
                                            <td>{{record.mappingType}}</td>
                                            <td>{{record.status}}</td>
                                            <td>{{record.value}}</td>
                                            <td>{{record.startDate}}</td>
                                            <td>{{record.endDate}}</td>
                                            <td>
                                                <span ng-if="record.isValid" class="validation-success">✓ Valid</span>
                                                <span ng-if="!record.isValid" class="validation-error">{{record.validationError}}</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div ng-show="bulkUploadData.length === 0 && !processingBulkUpload" class="empty-upload">
                        <div class="empty-icon">📤</div>
                        <div class="empty-text">No valid data found</div>
                        <div class="empty-subtext">Please check your file format and try again</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" ng-click="closeBulkUploadModal()">Cancel</button>
                <button class="btn btn-primary" ng-click="confirmBulkUpload()"
                        ng-show="!processingBulkUpload && bulkUploadResponse && getValidRecordsCount() > 0"
                        ng-disabled="processingBulkUpload">
                    📤 Upload {{getValidRecordsCount()}} Valid Records
                </button>
            </div>
        </div>
    </div>
</div>