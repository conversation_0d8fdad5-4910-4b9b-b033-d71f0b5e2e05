/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("versionManagementCtrl",
    function ($rootScope, $scope, $window, $http, $location, AppUtil, $cookieStore) {
        $rootScope.showFullScreenLoader = true;

        $scope.getSelectedCount = function() {
            if (!$scope.unitDetailList || $scope.unitDetailList.length === 0) {
                return 0;
            }
            var count = 0;
            for (var i = 0; i < $scope.unitDetailList.length; i++) {
                if ($scope.unitDetailList[i].checked) {
                    count++;
                }
            }
            return count;
        };
          $scope.customFilter = function(unit) {
              if (!$scope.searchText || $scope.searchText.trim() === '') {
                  return true;
              }
              var filter = $scope.searchText.toLowerCase();
              var unitName = unit.name ? unit.name.toLowerCase() : '';
              var unitId = unit.id != null ? unit.id.toString() : '';
              return unitName.includes(filter) || unitId.includes(filter);
          };

          // Watch for search text changes to reset pagination
          $scope.$watch('searchText', function() {
              $scope.currentPage = 1;
              $scope.totalPages = Math.ceil($scope.getFilteredUnits().length / $scope.itemsPerPage);
          });

          // Pagination functions
          $scope.setPage = function(pageNumber) {
              if (pageNumber < 1) {
                  $scope.currentPage = 1;
              } else if (pageNumber > $scope.totalPages) {
                  $scope.currentPage = $scope.totalPages;
              } else {
                  $scope.currentPage = pageNumber;
              }
          };

          $scope.getFilteredUnits = function() {
              return $scope.unitDetailList.filter($scope.customFilter);
          };

          $scope.getPaginatedUnits = function() {
              var filteredUnits = $scope.getFilteredUnits();

              // Sort by unit ID (numeric sort)
              filteredUnits.sort(function(a, b) {
                  return parseInt(a.id) - parseInt(b.id);
              });

              $scope.totalPages = Math.ceil(filteredUnits.length / $scope.itemsPerPage);

              var startIndex = ($scope.currentPage - 1) * $scope.itemsPerPage;
              var endIndex = startIndex + $scope.itemsPerPage;

              return filteredUnits.slice(startIndex, endIndex);
          };

          $scope.getPageNumbers = function() {
              var pages = [];
              var totalPages = $scope.totalPages;

              // Always show first 3 pages
              for (var i = 1; i <= Math.min(3, totalPages); i++) {
                  pages.push(i);
              }

              // Add ellipsis if needed
              if (totalPages > 3) {
                  pages.push('...');
                  // Add last page
                  pages.push(totalPages);
              }

              return pages;
          };


        $scope.init = function () {

            $scope.selectedRegionList = [];
            $scope.isGetUnitsClicked=false;
            $scope.unitDetailList=[];
            $scope.selectedAllPosVersion = null;
            $scope.selectedAllKettleCrmVersion = null;
            $scope.selectedAllTableServiceVersion = null;
            $scope.selectedAllMonkAppVersion = null;
            $scope.selectedAllKettleAttendanceAppVersion = null;
            $scope.selectedPosVersion=null;
            $scope.selectedCafeAppVersion=null;
            $scope.checkAll=false;
            $scope.appNameList = ["POS","CAFE_APP","TABLE_SERVICE","MONK_APP"];
            $scope.selectedAppNameList=[];
            $scope.allActiveVersionsAvailable = {};
            $scope.unitCategoryList = ["CAFE","COD","KITCHEN","DELIVERY","TAKE_AWAY","WAREHOUSE","OFFICE",];
            $scope.selectedUnitCategory = [];
            $scope.selectedToUpdate = [];

            // Pagination settings
            $scope.currentPage = 1;
            $scope.itemsPerPage = 10;
            $scope.totalPages = 1;
            $scope.Math = window.Math; // Make Math available in the template

            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.regions
            }).then(function success(response) {
                $scope.regionList = response.data;
                console.log($scope.regionList);
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
            });

            // The allUnitsList API will be called when the Get Units button is clicked
            $scope.unitlist = [];

             $http({
                  method: 'GET',
                  url: AppUtil.restUrls.versionManagement.getAllUnitEvents
             }).then(function success(response) {
                  $scope.unitAllActiveEventList = response.data;
                  for (var key in $scope.unitAllActiveEventList) {
                    var list = $scope.unitAllActiveEventList[key];
                    var application = [];
                    var i = list.length-1;
                    while(i >= 0){
                       if(application.includes(list[i].applicationName)){
                            list.splice(i,1);
                       }
                       else{
                           application.push(list[i].applicationName);
                           i--;
                           continue;
                       }
                       i--;
                    }
                    $scope.unitAllActiveEventList[key] = list;
                  }
                  console.log("allActiveUnitEvents=", $scope.unitAllActiveEventList);
                  $rootScope.showFullScreenLoader = false;
             }, function error(response) {
                  $rootScope.showFullScreenLoader = false;
                  console.log("error:" + response);
             });

             $http({
                   method: 'GET',
                   url: AppUtil.restUrls.versionManagement.getAllActiveVersions
                   }).then(function success(response) {
                       $scope.allActiveVersionsAvailable = response.data;
                       console.log("allActiveVersions=", $scope.allActiveVersionsAvailable);
                       $rootScope.showFullScreenLoader = false;
                   }, function error(response) {
                       $rootScope.showFullScreenLoader = false;
                       console.log("error:" + response);
              });
        };

        $scope.multiSelectSettings = {
            showEnableSearchButton: true, template: '<b> {{option}}</b>', scrollable: true,
            scrollableHeight: '200px'
        };

        $scope.getUnitDetails = function(){
            $rootScope.showFullScreenLoader = true;

            // Call the get-all-unit API
            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.allUnitsList
            }).then(function success(response) {
                $scope.unitlist = response.data;
                for(var i=0;i<$scope.unitlist.length;i++){
                     if($scope.unitlist[i].status !=='ACTIVE'){
                         $scope.unitlist.splice(i,1);
                     }
                }
                console.log("allUnits refreshed=", $scope.unitlist);

                // Apply filters after getting fresh data
                $scope.unitDetailList = [];
                if($scope.selectedRegionList.length === 0){
                    $scope.unitDetailList = $scope.unitlist;
                }
                else{
                    for(var i=0;i<$scope.unitlist.length;i++){
                        if($scope.selectedRegionList.includes($scope.unitlist[i].region) && $scope.unitlist[i].status === 'ACTIVE'){
                            $scope.unitDetailList.push($scope.unitlist[i]);
                        }
                    }
                }

                // Apply unit category filter if selected
                var x = [];
                if($scope.selectedUnitCategory.length !== 0){
                    for(var i=0;i<$scope.unitDetailList.length;i++){
                       if($scope.selectedUnitCategory.includes($scope.unitDetailList[i].category) && $scope.unitDetailList[i].status === 'ACTIVE'){
                         x.push($scope.unitDetailList[i])
                       }
                    }
                    $scope.unitDetailList = x;
                }

                // Reset pagination to first page when getting new data
                $scope.currentPage = 1;
                $scope.totalPages = Math.ceil($scope.getFilteredUnits().length / $scope.itemsPerPage);

                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                $rootScope.showFullScreenLoader = false;
                console.log("error:" + response);
                alert("Error fetching unit data");
            });
        }


        $scope.updateAll = function(isChecked){
            $scope.checkAll = isChecked;
            for(var i=0;i<$scope.unitDetailList.length;i++){
                if(isChecked){
                  $scope.unitDetailList[i].checked = true;

                  // Apply selected versions to all checked units
                  if($scope.selectedAllPosVersion || $scope.selectedAllKettleCrmVersion) {
                      var unitVersions = $scope.unitAllActiveEventList[$scope.unitDetailList[i].id];
                      if(unitVersions && unitVersions.length > 0) {
                          for(var j=0; j<unitVersions.length; j++) {
                              // Apply POS version if selected
                              if(unitVersions[j].applicationName === 'POS' && $scope.selectedAllPosVersion) {
                                  unitVersions[j].changedVersion = $scope.selectedAllPosVersion;
                              }
                              // Apply CAFE_APP version if selected and POS version is selected
                              if(unitVersions[j].applicationName === 'CAFE_APP' && $scope.selectedAllKettleCrmVersion) {
                                  // Only set CAFE_APP version if POS version is selected
                                  if($scope.selectedAllPosVersion && $scope.selectedAllPosVersion !== "") {
                                      unitVersions[j].changedVersion = $scope.selectedAllKettleCrmVersion;
                                  } else {
                                      // Don't set CAFE_APP version if POS version is not selected
                                      console.log("Cannot set CAFE_APP version without POS version for unit", $scope.unitDetailList[i].id);
                                  }
                              }
                              // Apply TABLE_SERVICE version if selected
                              if(unitVersions[j].applicationName === 'TABLE_SERVICE' && $scope.selectedAllTableServiceVersion) {
                                  unitVersions[j].changedVersion = $scope.selectedAllTableServiceVersion;
                              }
                              // Apply MONK_APP version if selected
                              if(unitVersions[j].applicationName === 'MONK_APP' && $scope.selectedAllMonkAppVersion) {
                                  unitVersions[j].changedVersion = $scope.selectedAllMonkAppVersion;
                              }
                              // Apply KETTLE_ATTENDANCE version if selected
                              if(unitVersions[j].applicationName === 'KETTLE_ATTENDANCE' && $scope.selectedAllKettleAttendanceAppVersion) {
                                  unitVersions[j].changedVersion = $scope.selectedAllKettleAttendanceAppVersion;
                              }
                          }
                      }
                  }
                } else {
                  $scope.unitDetailList[i].checked = false;

                  // Clear all version selections when unchecked
                  var unitVersions = $scope.unitAllActiveEventList[$scope.unitDetailList[i].id];
                  if(unitVersions && unitVersions.length > 0) {
                      for(var j=0; j<unitVersions.length; j++) {
                          unitVersions[j].changedVersion = null;
                      }
                  }
                }
            }
        }

        $scope.changeAllPos = function(version){
            $scope.selectedAllPosVersion = version;
        }

        $scope.changeAllCrm = function(version){
            $scope.selectedAllKettleCrmVersion = version;
        }

        $scope.findCompatibleCrmVersions = function(application, selectedVersion) {
            console.log("findCompatibleCrmVersions called for", application, "with version", selectedVersion);

            if(application === "POS"){
                $scope.selectedAllPosVersion = selectedVersion;
                console.log("Set selectedAllPosVersion to", selectedVersion);

                $http({
                  method: 'GET',
                  url: AppUtil.restUrls.versionManagement.getCompatibilityVersions + "?applicationName=CAFE_APP&posVersion=" + $scope.selectedAllPosVersion,
                }).then(function success(response) {
                  $scope.compatibleVersions = response.data;
                  if($scope.compatibleVersions !== null && $scope.compatibleVersions !== undefined){
                    $scope.allActiveVersionsAvailable['CAFE_APP'] = $scope.compatibleVersions;
                  }
                  $scope.selectedAllKettleCrmVersion = "";

                  // Apply the selected POS version to all checked units
                  $scope.applyVersionToCheckedUnits('POS', $scope.selectedAllPosVersion);

                }, function error(response) {
                  $rootScope.showFullScreenLoader = false;
                  alert("Error while updating");
                  console.log("error:" + response);
                });
            }
            else if(application === "CAFE_APP"){
                // Check if POS version is selected before setting CAFE_APP version
                if(!$scope.selectedAllPosVersion || $scope.selectedAllPosVersion === "") {
                    alert("Please select the POS version first");
                    // Reset the CAFE_APP dropdown to empty using $timeout to ensure it happens after the digest cycle
                    setTimeout(function() {
                        $scope.$apply(function() {
                            $scope.selectedAllKettleCrmVersion = "";
                        });
                    }, 0);
                    return;
                }

                $scope.selectedAllKettleCrmVersion = selectedVersion;
                // Apply the selected CAFE_APP version to all checked units
                $scope.applyVersionToCheckedUnits('CAFE_APP', $scope.selectedAllKettleCrmVersion);
            }
            else if(application === "TABLE_SERVICE"){
                $scope.selectedAllTableServiceVersion = selectedVersion;
                // Apply the selected TABLE_SERVICE version to all checked units
                $scope.applyVersionToCheckedUnits('TABLE_SERVICE', $scope.selectedAllTableServiceVersion);
            }
            else if(application === "MONK_APP"){
                $scope.selectedAllMonkAppVersion = selectedVersion;

                // Apply the selected MONK_APP version to all checked units
                $scope.applyVersionToCheckedUnits('MONK_APP', $scope.selectedAllMonkAppVersion);
            }
            else if(application === "KETTLE_ATTENDANCE"){
                $scope.selectedAllKettleAttendanceAppVersion = selectedVersion;
                // Apply the selected KETTLE_ATTENDANCE version to all checked units
                $scope.applyVersionToCheckedUnits('KETTLE_ATTENDANCE', $scope.selectedAllKettleAttendanceAppVersion);
            }

            // Check if any units are selected, if not, select at least one
//            if ($scope.getSelectedCount() === 0) {
//                if ($scope.unitDetailList && $scope.unitDetailList.length > 0) {
//                    $scope.unitDetailList[0].checked = true;
//                    console.log("Selected unit:", $scope.unitDetailList[0].id);
//                }
//            }
        };

        // Helper function to apply a version to all checked units
        $scope.applyVersionToCheckedUnits = function(appName, version) {
            if(!version) {
                return;
            }

            // For CAFE_APP, check if POS version is selected
            if(appName === 'CAFE_APP' && (!$scope.selectedAllPosVersion || $scope.selectedAllPosVersion === "")) {
                console.log("Cannot apply CAFE_APP version without POS version");
                return;
            }

            var updatedCount = 0;

            for(var i=0; i<$scope.unitDetailList.length; i++){
                if($scope.unitDetailList[i].checked){
                    var unitId = $scope.unitDetailList[i].id;
                    console.log("Processing checked unit:", unitId);

                    var unitVersions = $scope.unitAllActiveEventList[unitId];
                    if(unitVersions && unitVersions.length > 0) {
                        for(var j=0; j<unitVersions.length; j++) {
                            if(unitVersions[j].applicationName === appName) {
                                // For CAFE_APP, double-check POS version for this specific unit
                                if(appName === 'CAFE_APP') {
                                    var hasPosVersion = false;
                                    for(var k=0; k<unitVersions.length; k++) {
                                        if(unitVersions[k].applicationName === 'POS' && unitVersions[k].changedVersion) {
                                            hasPosVersion = true;
                                            break;
                                        }
                                    }
                                    if(!hasPosVersion) {
                                        console.log("Skipping CAFE_APP update for unit", unitId, "because it has no POS version selected");
                                        continue;
                                    }
                                }

                                unitVersions[j].changedVersion = version;
                                updatedCount++;
                                console.log("Updated", appName, "version to", version, "for unit", unitId);
                            }
                        }
                    } else {
                        console.log("No versions found for unit", unitId);
                    }
                }
            }

            console.log("Total units updated:", updatedCount);
        };


        $scope.toShow = function(version){
            return $scope.selectedAppNameList.length === 0 ||
                   $scope.selectedAppNameList.includes(version.applicationName);
        }

        $scope.changeRow = function(isChecked, detail){
            for(var i=0; i<$scope.unitDetailList.length; i++){
               if($scope.unitDetailList[i].id === detail.id){
                  $scope.unitDetailList[i].checked = isChecked;

                  var unitVersions = $scope.unitAllActiveEventList[detail.id];
                  if(unitVersions && unitVersions.length > 0) {
                      // If checkbox is checked and versions are selected, update the unit's versions
                      if(isChecked) {
                          for(var j=0; j<unitVersions.length; j++) {
                              // Update POS version if available
                              if(unitVersions[j].applicationName === 'POS' && $scope.selectedAllPosVersion) {
                                  unitVersions[j].changedVersion = $scope.selectedAllPosVersion;
                              }
                              // Update CAFE_APP version if available and POS version is selected
                              if(unitVersions[j].applicationName === 'CAFE_APP' && $scope.selectedAllKettleCrmVersion) {
                                  // Only set CAFE_APP version if POS version is selected
                                  if($scope.selectedAllPosVersion && $scope.selectedAllPosVersion !== "") {
                                      unitVersions[j].changedVersion = $scope.selectedAllKettleCrmVersion;
                                  } else {
                                      // Don't set CAFE_APP version if POS version is not selected
                                      console.log("Cannot set CAFE_APP version without POS version for unit", detail.id);
                                  }
                              }
                              // Update TABLE_SERVICE version if available
                              if(unitVersions[j].applicationName === 'TABLE_SERVICE' && $scope.selectedAllTableServiceVersion) {
                                  unitVersions[j].changedVersion = $scope.selectedAllTableServiceVersion;
                              }
                              // Update MONK_APP version if available
                              if(unitVersions[j].applicationName === 'MONK_APP' && $scope.selectedAllMonkAppVersion) {
                                  unitVersions[j].changedVersion = $scope.selectedAllMonkAppVersion;
                              }
                              // Update KETTLE_ATTENDANCE version if available
                              if(unitVersions[j].applicationName === 'KETTLE_ATTENDANCE' && $scope.selectedAllKettleAttendanceAppVersion) {
                                  unitVersions[j].changedVersion = $scope.selectedAllKettleAttendanceAppVersion;
                              }
                          }
                      }
                      // If checkbox is unchecked, clear all version selections
                      else {
                          for(var j=0; j<unitVersions.length; j++) {
                              unitVersions[j].changedVersion = null;
                          }
                      }
                  }
               }
           }
        }



        $scope.findCompatibleCrmVersionsForUnit = function(version){
                console.log("findCompatibleCrmVersionsForUnit called for", version.applicationName, "with version", version.changedVersion, "for unit", version.unitId);

                // Check the unit with this version
                for(var i=0; i<$scope.unitDetailList.length; i++){
                    if($scope.unitDetailList[i].id === version.unitId){
                         $scope.unitDetailList[i].checked = true;
                         console.log("Checked unit:", version.unitId);
                    }
                }

                // Update the global version selection based on the application type
                if(version.applicationName === "POS"){
                    $scope.selectedAllPosVersion = version.changedVersion;
                    console.log("Set selectedAllPosVersion to", version.changedVersion);

                    // Get compatible CAFE_APP versions for this POS version
                    $http({
                       method: 'GET',
                       url: AppUtil.restUrls.versionManagement.getCompatibilityVersions + "?applicationName=CAFE_APP&posVersion=" + version.changedVersion,
                    }).then(function success(response) {
                        $scope.compatibleVersions = response.data;
                        if($scope.compatibleVersions !== null && $scope.compatibleVersions !== undefined){
                            $scope.allActiveVersionsAvailable['CAFE_APP'] = $scope.compatibleVersions;
                        }
                        $scope.selectedAllKettleCrmVersion = "";
                    }, function error(response) {
                       $rootScope.showFullScreenLoader = false;
                       alert("Error while updating");
                       console.log("error:" + response);
                    });
                } else if(version.applicationName === "CAFE_APP") {
                    // Check if POS version is selected for CAFE_APP
                    var unitVal = $scope.unitAllActiveEventList[version.unitId];
                    var posMissing = true;

                    for(var i = 0; i<unitVal.length; i++){
                        if(unitVal[i].applicationName === "POS" && unitVal[i].changedVersion){
                            posMissing = false;
                            break;
                        }
                    }

                    if(posMissing) {
                        alert("Please select the POS version first");
                        // Reset the CAFE_APP version for this unit
                        setTimeout(function() {
                            $scope.$apply(function() {
                                for(var i = 0; i<unitVal.length; i++){
                                    if(unitVal[i].applicationName === "CAFE_APP") {
                                        unitVal[i].changedVersion = "";
                                    }
                                }
                                // Also reset the global CAFE_APP version if it matches
                                if($scope.selectedAllKettleCrmVersion === version.changedVersion) {
                                    $scope.selectedAllKettleCrmVersion = "";
                                }
                            });
                        }, 0);
                        return;
                    }

                    $scope.selectedAllKettleCrmVersion = version.changedVersion;
                } else if(version.applicationName === "TABLE_SERVICE") {
                    $scope.selectedAllTableServiceVersion = version.changedVersion;
                } else if(version.applicationName === "MONK_APP") {
                    $scope.selectedAllMonkAppVersion = version.changedVersion;
                }
                else if(version.applicationName === "KETTLE_ATTENDANCE") {
                    $scope.selectedAllKettleAttendanceAppVersion = version.changedVersion;
                }
        };

        $scope.updateSelectedUnitVersion = function(detail){
           $rootScope.showFullScreenLoader = true;
            for(var i =0;i<$scope.unitDetailList.length;i++){
                if($scope.unitDetailList[i].checked){
                    var detail = $scope.unitDetailList[i];
                    if($scope.unitAllActiveEventList[detail.id] !== undefined && $scope.unitAllActiveEventList[detail.id] !== null){
                      for(var j =0;j<$scope.unitAllActiveEventList[detail.id].length;j++){
                        if($scope.unitAllActiveEventList[detail.id][j].changedVersion !== undefined && $scope.unitAllActiveEventList[detail.id][j].changedVersion !==null &&
                             $scope.unitAllActiveEventList[detail.id][j].changedVersion !== $scope.unitAllActiveEventList[detail.id][j].applicationVersion){
                             var eventDomainObj = {
                                unitId : detail.id,
                                applicationName : $scope.unitAllActiveEventList[detail.id][j].applicationName,
                                applicationVersion : $scope.unitAllActiveEventList[detail.id][j].changedVersion,
                                unitRegion : detail.region,
                                updatedBy : $rootScope.userData.id,
                                updatedTime : null,
                                status : null
                             }
                             $scope.selectedToUpdate.push(eventDomainObj);
                        }
                      }
                   }
                   if($scope.selectedAllPosVersion !== null && $scope.selectedAllPosVersion !== undefined && $scope.selectedAllPosVersion !==""){
                       var eventDomainObj = {
                                 unitId : detail.id,
                                 applicationName : "POS",
                                 applicationVersion : $scope.selectedAllPosVersion,
                                 unitRegion : detail.region,
                                 updatedBy : $rootScope.userData.id,
                                 updatedTime : null,
                                 status : null
                            }
                       $scope.selectedToUpdate.push(eventDomainObj);
                   }
                   // Only add CAFE_APP version if POS version is also selected
                   if($scope.selectedAllKettleCrmVersion !== null && $scope.selectedAllKettleCrmVersion !== undefined && $scope.selectedAllKettleCrmVersion !== ""){
                       // Check if POS version is selected
                       if($scope.selectedAllPosVersion && $scope.selectedAllPosVersion !== "") {
                           var eventDomainObj = {
                                    unitId : detail.id,
                                    applicationName : "CAFE_APP",
                                    applicationVersion : $scope.selectedAllKettleCrmVersion,
                                    unitRegion : detail.region,
                                    updatedBy : $rootScope.userData.id,
                                    updatedTime : null,
                                    status : null
                                }
                           $scope.selectedToUpdate.push(eventDomainObj);
                       } else {
                           console.log("Skipping CAFE_APP update for unit", detail.id, "because no POS version is selected");
                       }
                   }
                }
            }
            if($scope.selectedToUpdate <=0){
                alert("No version selected to update");
                $rootScope.showFullScreenLoader = false;
                return;
            }
            alert("Confirm to update the unit");
            console.log("TO_UPDATE",$scope.selectedToUpdate);
            $http({
               method: 'POST',
               url: AppUtil.restUrls.versionManagement.createUpdateVersionEvent,
               data : $scope.selectedToUpdate
            }).then(function success(response) {
               $scope.eventsCreated = response.data;
               $rootScope.showFullScreenLoader = false;
               if($scope.eventsCreated >= 0){
               $scope.init();
               }else{
                alert("Update cannot be done");
               }
               }, function error(response) {
               $rootScope.showFullScreenLoader = false;
               alert("Error while updating");
               console.log("error:" + response);
            });

        }

        $scope.updateAllVersionsSelected = function(){
            $scope.selectedToUpdate = [];

//            // If no units are selected, select the first one
//            if ($scope.getSelectedCount() === 0 && $scope.unitDetailList && $scope.unitDetailList.length > 0) {
//                $scope.unitDetailList[0].checked = true;
//                console.log("No units were selected, automatically selected the first unit:", $scope.unitDetailList[0].id);
//            }

            for(var i=0; i<$scope.unitDetailList.length; i++){
                var unitId = $scope.unitDetailList[i].id;
                if($scope.unitDetailList[i].checked === true){
                    console.log("Processing checked unit:", unitId);

                    // Process each application type
                    if($scope.selectedAllPosVersion !== null && $scope.selectedAllPosVersion !== undefined && $scope.selectedAllPosVersion !== ""){
                        var eventDomainObj = {
                            unitId: unitId,
                            unitRegion: $scope.unitDetailList[i].region,
                            applicationName: "POS",
                            applicationVersion: $scope.selectedAllPosVersion,
                            updatedBy: $rootScope.userData.id,
                            updatedTime: null,
                            status: null
                        };
                        $scope.selectedToUpdate.push(eventDomainObj);
                        console.log("Added POS update for unit", unitId);
                    }

                    // Only add CAFE_APP version if POS version is also selected
                    if($scope.selectedAllKettleCrmVersion !== null && $scope.selectedAllKettleCrmVersion !== undefined && $scope.selectedAllKettleCrmVersion !== ""){
                        // Check if POS version is selected
                        if($scope.selectedAllPosVersion && $scope.selectedAllPosVersion !== "") {
                            var eventDomain = {
                                unitId: unitId,
                                unitRegion: $scope.unitDetailList[i].region,
                                applicationName: "CAFE_APP",
                                applicationVersion: $scope.selectedAllKettleCrmVersion,
                                updatedBy: $rootScope.userData.id,
                                updatedTime: null,
                                status: null
                            };
                            $scope.selectedToUpdate.push(eventDomain);
                        } else {
                            console.log("Skipping CAFE_APP update for unit", unitId, "because no POS version is selected");
                        }
                    }

                    if($scope.selectedAllTableServiceVersion !== null && $scope.selectedAllTableServiceVersion !== undefined && $scope.selectedAllTableServiceVersion !== ""){
                        var eventDomainTableService = {
                            unitId: unitId,
                            unitRegion: $scope.unitDetailList[i].region,
                            applicationName: "TABLE_SERVICE",
                            applicationVersion: $scope.selectedAllTableServiceVersion,
                            updatedBy: $rootScope.userData.id,
                            updatedTime: null,
                            status: null
                        };
                        $scope.selectedToUpdate.push(eventDomainTableService);
                    }

                    if($scope.selectedAllMonkAppVersion !== null && $scope.selectedAllMonkAppVersion !== undefined && $scope.selectedAllMonkAppVersion !== ""){
                        var eventDomainMonkApp = {
                            unitId: unitId,
                            unitRegion: $scope.unitDetailList[i].region,
                            applicationName: "MONK_APP",
                            applicationVersion: $scope.selectedAllMonkAppVersion,
                            updatedBy: $rootScope.userData.id,
                            updatedTime: null,
                            status: null
                        };
                        $scope.selectedToUpdate.push(eventDomainMonkApp);
                    }
                    if($scope.selectedAllKettleAttendanceAppVersion !== null && $scope.selectedAllKettleAttendanceAppVersion !== undefined && $scope.selectedAllKettleAttendanceAppVersion !== ""){
                        var eventDomainKettleAttendance = {
                            unitId: unitId,
                            unitRegion: $scope.unitDetailList[i].region,
                            applicationName: "KETTLE_ATTENDANCE",
                            applicationVersion: $scope.selectedAllKettleAttendanceAppVersion,
                            updatedBy: $rootScope.userData.id,
                            updatedTime: null,
                            status: null
                        };
                        $scope.selectedToUpdate.push(eventDomainKettleAttendance);
                    }
                }
            }

            // Check if there are any updates to process
            if($scope.selectedToUpdate.length === 0) {
                console.log("No updates to process. Checking why...");

                // Check if any versions are selected
                var anyVersionSelected =
                    ($scope.selectedAllPosVersion && $scope.selectedAllPosVersion !== "") ||
                    ($scope.selectedAllKettleCrmVersion && $scope.selectedAllKettleCrmVersion !== "") ||
                    ($scope.selectedAllTableServiceVersion && $scope.selectedAllTableServiceVersion !== "") ||
                    ($scope.selectedAllMonkAppVersion && $scope.selectedAllMonkAppVersion !== "") ||
                    ($scope.selectedAllKettleAttendanceAppVersion && $scope.selectedAllKettleAttendanceAppVersion !== "");

                if (!anyVersionSelected) {
                    alert("No versions selected. Please select at least one version to update.");
                    return;
                }

                // Check if any units are checked
                if ($scope.getSelectedCount() === 0) {
                    alert("No units selected. Please select at least one unit to update.");
                    return;
                }

                // If we get here, something else is wrong
                console.log("Both versions and units are selected, but no updates were created.");
                console.log("Forcing update with available data...");

                // Force add updates for all checked units and selected versions
                for(var i=0; i<$scope.unitDetailList.length; i++){
                    if($scope.unitDetailList[i].checked){
                        var unitId = $scope.unitDetailList[i].id;
                        var unitRegion = $scope.unitDetailList[i].region;

                        if($scope.selectedAllTableServiceVersion && $scope.selectedAllTableServiceVersion !== ""){
                            var tsUpdate = {
                                unitId: unitId,
                                unitRegion: unitRegion,
                                applicationName: "TABLE_SERVICE",
                                applicationVersion: $scope.selectedAllTableServiceVersion,
                                updatedBy: $rootScope.userData.id,
                                updatedTime: null,
                                status: null
                            };
                            $scope.selectedToUpdate.push(tsUpdate);
                            console.log("Force added TABLE_SERVICE update for unit", unitId, "with version", $scope.selectedAllTableServiceVersion);
                        }

                        if($scope.selectedAllMonkAppVersion && $scope.selectedAllMonkAppVersion !== ""){
                            var maUpdate = {
                                unitId: unitId,
                                unitRegion: unitRegion,
                                applicationName: "MONK_APP",
                                applicationVersion: $scope.selectedAllMonkAppVersion,
                                updatedBy: $rootScope.userData.id,
                                updatedTime: null,
                                status: null
                            };
                            $scope.selectedToUpdate.push(maUpdate);
                            console.log("Force added MONK_APP update for unit", unitId, "with version", $scope.selectedAllMonkAppVersion);
                        }
                        if($scope.selectedAllKettleAttendanceAppVersion && $scope.selectedAllKettleAttendanceAppVersion !== ""){
                            var kaUpdate = {
                                unitId: unitId,
                                unitRegion: unitRegion,
                                applicationName: "KETTLE_ATTENDANCE",
                                applicationVersion: $scope.selectedAllKettleAttendanceAppVersion,
                                updatedBy: $rootScope.userData.id,
                                updatedTime: null,
                                status: null
                            };
                            $scope.selectedToUpdate.push(kaUpdate);
                            console.log("Force added KETTLE_ATTENDANCE update for unit", unitId, "with version", $scope.selectedAllKettleAttendanceAppVersion);
                        }
                    }
                }

                // If still no updates, show error
                if($scope.selectedToUpdate.length === 0) {
                    alert("Unable to create updates. Please try again or contact support.");
                    return;
                }
            }
            if($scope.selectedToUpdate <=0){
                alert("No version selected to update");
                $rootScope.showFullScreenLoader = false;
                return;
            }
            alert("Confirm to update the selected units");
            console.log("Updates to be processed:", $scope.selectedToUpdate);

            $http({
                method: 'POST',
                url: AppUtil.restUrls.versionManagement.createUpdateVersionEvent,
                data: $scope.selectedToUpdate
            }).then(function success(response) {
                $scope.eventsCreated = response.data;
                $rootScope.showFullScreenLoader = false;
                if($scope.eventsCreated >= 0){
                    $scope.init();
                } else {
                    alert("Update cannot be done");
                }
            }, function error(response) {
                $rootScope.showFullScreenLoader = false;
                alert("Error while updating");
                console.log("error:" + response);
            });
        }
});
